import type { PageServerLoad } from './$types';

export interface PageData {
  signedDocument: {
    id: string;
    document_id: string;
    creator_id: string;
    signer_id: string;
    status: string;
    reference_number: string;
    signature: any;
    rejection_reason?: string;
    revision_comments?: string;
    signed_at?: string;
    created_at: string;
    updated_at: string;
    recipient_id?: string;
    recipient_unit_id?: string;
  } | null;
  documentData: {
    title: string;
    content?: string;
    content_url?: string;
  } | null;
  documentId: string;
}
