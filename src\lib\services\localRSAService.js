/**
 * خدمة المفاتيح المحلية - حل احتياطي عندما لا تتوفر مفاتيح في قاعدة البيانات
 */

import forge from 'node-forge';

// مفاتيح RSA المحلية المؤقتة
const LOCAL_RSA_KEYS = {
  privateKey: `-----BEGIN RSA PRIVATE KEY-----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-----END RSA PRIVATE KEY-----`,
  
  publicKey: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwxqSlfmiJ/ZUtlgWySvk
bMmpEdDs7tDAHTLd7Jo97Obd/wbWnTkrazt7/MnSCpYlZcczz6K7uS8D8pbJTRwq
XVYtRfAyrFIAQGxEJk88elj1xd5DuiucPHnG0FbDQBs3q4lXiMaeD8yr4nAO51tj
3QQFyN+LzO/W/Pwf8qYPV8ssK/QtqmUp47uioGaW/d9KqJhxVZnm1bhWJSOBnv8x
SfQbTVL6kS/MirUJdwna35cr/hjbyYN5uZH/DlQZC9KySFv+GbEYi7afP8iNloJG
tXwvClEZTcASog19uSmeSguLc/FND8Xpl3KMR8sFevddyoIOS1HtNE9z6Mj9/nvf
SQIDAQAB
-----END PUBLIC KEY-----`
};

/**
 * خدمة المفاتيح المحلية
 */
export class LocalRSAService {
  /**
   * الحصول على المفاتيح المحلية
   * @returns {Object} - زوج المفاتيح المحلية
   */
  static getLocalKeys() {
    try {
      // تحويل المفاتيح من تنسيق PEM إلى كائنات
      const privateKeyObj = forge.pki.privateKeyFromPem(LOCAL_RSA_KEYS.privateKey);
      const publicKeyObj = forge.pki.publicKeyFromPem(LOCAL_RSA_KEYS.publicKey);
      
      return {
        privateKey: LOCAL_RSA_KEYS.privateKey,
        publicKey: LOCAL_RSA_KEYS.publicKey,
        privateKeyObj: privateKeyObj,
        publicKeyObj: publicKeyObj
      };
    } catch (error) {
      console.error('خطأ في تحميل المفاتيح المحلية:', error);
      throw error;
    }
  }
  
  /**
   * التحقق من صحة المفاتيح المحلية
   * @returns {boolean} - هل المفاتيح صحيحة
   */
  static validateLocalKeys() {
    try {
      const keys = this.getLocalKeys();
      
      // اختبار بسيط للتوقيع والتحقق
      const testMessage = 'test-message';
      const md = forge.md.sha256.create();
      md.update(testMessage, 'utf8');
      
      // توقيع الرسالة
      const signature = keys.privateKeyObj.sign(md);
      
      // التحقق من التوقيع
      const isValid = keys.publicKeyObj.verify(md.digest().bytes(), signature);
      
      return isValid;
    } catch (error) {
      console.error('خطأ في التحقق من المفاتيح المحلية:', error);
      return false;
    }
  }
  
  /**
   * طباعة معلومات المفاتيح المحلية
   */
  static printKeyInfo() {
    try {
      const keys = this.getLocalKeys();
      console.log('🔑 معلومات المفاتيح المحلية:');
      console.log('   - المفتاح الخاص: متوفر');
      console.log('   - المفتاح العام: متوفر');
      console.log('   - حالة التحقق:', this.validateLocalKeys() ? '✅ صحيح' : '❌ خطأ');
    } catch (error) {
      console.error('❌ خطأ في طباعة معلومات المفاتيح:', error);
    }
  }
}
