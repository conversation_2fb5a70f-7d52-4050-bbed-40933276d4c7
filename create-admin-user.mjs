import { createClient } from '@supabase/supabase-js';

console.log('👤 إنشاء مستخدم مشرف...');
console.log('=' .repeat(40));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// بيانات المستخدم المشرف
const adminUser = {
  email: '<EMAIL>',
  password: 'AdminRSA123!@#',
  full_name: 'مدير النظام - RSA',
  role: 'مشرف'
};

async function createAdminUser() {
  try {
    console.log('📧 إنشاء حساب المشرف...');
    console.log(`   البريد الإلكتروني: ${adminUser.email}`);

    // محاولة تسجيل مستخدم جديد
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: adminUser.email,
      password: adminUser.password,
      options: {
        data: {
          full_name: adminUser.full_name,
          role: adminUser.role
        },
        emailRedirectTo: undefined // تعطيل إعادة التوجيه
      }
    });

    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.log('ℹ️ المستخدم موجود بالفعل، محاولة تسجيل الدخول...');
      } else {
        console.error('❌ فشل إنشاء المستخدم:', signUpError.message);
        return false;
      }
    } else {
      console.log('✅ تم إنشاء المستخدم بنجاح');
      if (signUpData.user && !signUpData.user.email_confirmed_at) {
        console.log('📧 ملاحظة: قد تحتاج لتأكيد البريد الإلكتروني');
      }
    }

    console.log('\n🔐 تسجيل الدخول...');

    // محاولة تسجيل الدخول
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: adminUser.email,
      password: adminUser.password
    });

    if (signInError) {
      console.error('❌ فشل تسجيل الدخول:', signInError.message);

      if (signInError.message.includes('Email not confirmed')) {
        console.log('💡 البريد الإلكتروني غير مؤكد');
        console.log('🔧 محاولة تجاوز التأكيد...');

        // في بيئة التطوير، يمكن تجاهل تأكيد البريد
        return await handleUnconfirmedEmail();
      }

      return false;
    }

    console.log('✅ تم تسجيل الدخول بنجاح');

    // التحقق من المستخدم
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('❌ خطأ في جلب بيانات المستخدم:', userError?.message);
      return false;
    }

    console.log(`👤 المستخدم: ${user.email}`);
    console.log(`🆔 المعرف: ${user.id}`);

    console.log('\n📋 إنشاء/تحديث الملف الشخصي...');

    // إنشاء أو تحديث الملف الشخصي
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        id: user.id,
        email: user.email,
        full_name: adminUser.full_name,
        role: adminUser.role,
        updated_at: new Date().toISOString()
      });

    if (profileError) {
      console.error('❌ خطأ في إنشاء الملف الشخصي:', profileError.message);
      console.log('⚠️ سيتم المتابعة بدون ملف شخصي');
    } else {
      console.log('✅ تم إنشاء الملف الشخصي بنجاح');
    }

    console.log('\n🎉 تم إعداد المستخدم المشرف بنجاح!');
    console.log('\n📝 بيانات تسجيل الدخول:');
    console.log(`   البريد الإلكتروني: ${adminUser.email}`);
    console.log(`   كلمة المرور: ${adminUser.password}`);
    console.log('\n💡 يمكنك الآن إضافة مفاتيح RSA إلى قاعدة البيانات');

    return true;

  } catch (error) {
    console.error('💥 خطأ عام:', error.message);
    return false;
  }
}

async function handleUnconfirmedEmail() {
  console.log('🔄 محاولة حل مشكلة البريد غير المؤكد...');

  // في بيئة التطوير، يمكن استخدام حساب مؤقت
  console.log('💡 استخدم واجهة التطبيق لتسجيل الدخول');
  console.log('💡 أو تحقق من إعدادات Supabase لتعطيل تأكيد البريد');

  return false;
}

createAdminUser();
