import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';

// إعداد عميل Supabase للخادم
const supabaseUrl = 'https://bgbzirxgewwidgybccxq.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJnYnppcnhnZXd3aWRneWJjY3hxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcxNzUyMDQ3NiwiZXhwIjoyMDMzMDk2NDc2fQ.VY7OOlZtlXCXmPHleR0-3SoJoFOmHlmhKdXhKMQeL7s';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

export const load: PageServerLoad = async ({ params, url, locals }) => {
  try {
    const documentId = params.id;
    
    if (!documentId) {
      throw error(400, 'معرف المستند غير صحيح');
    }

    console.log('🔍 Server: تحميل المستند الموقع:', documentId);

    // محاولة جلب المستند من view أولاً
    let { data: signedDocument, error: fetchError } = await supabase
      .from('signed_documents_with_details')
      .select('*')
      .eq('id', documentId)
      .single();

    // إذا فشل مع view، جرب الجدول الأساسي
    if (fetchError && fetchError.message.includes('does not exist')) {
      console.log('🔄 Server: View غير موجود، جرب الجدول الأساسي...');
      
      const { data: basicData, error: basicError } = await supabase
        .from('signed_documents')
        .select('*')
        .eq('id', documentId)
        .single();

      signedDocument = basicData;
      fetchError = basicError;
    }

    if (fetchError) {
      console.error('❌ Server: خطأ في جلب المستند:', fetchError);
      throw error(500, `خطأ في جلب المستند: ${fetchError.message}`);
    }

    if (!signedDocument) {
      console.error('❌ Server: المستند غير موجود');
      throw error(404, 'المستند غير موجود');
    }

    // جلب بيانات المستند الأصلي
    let documentData = null;
    if (signedDocument.document_id) {
      const { data: docData, error: docError } = await supabase
        .from('documents')
        .select('title, content, content_url')
        .eq('id', signedDocument.document_id)
        .single();

      if (!docError && docData) {
        documentData = docData;
      }
    }

    console.log('✅ Server: تم تحميل البيانات بنجاح');

    return {
      signedDocument,
      documentData,
      documentId
    };

  } catch (err: any) {
    console.error('💥 Server: خطأ في تحميل الصفحة:', err);
    
    if (err.status) {
      throw err; // إعادة رمي أخطاء SvelteKit
    }
    
    throw error(500, `خطأ داخلي في الخادم: ${err.message || 'خطأ غير معروف'}`);
  }
};
