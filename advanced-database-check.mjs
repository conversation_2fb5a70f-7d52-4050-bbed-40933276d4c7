import { createClient } from '@supabase/supabase-js';

console.log('🔍 فحص متقدم لقاعدة البيانات...');
console.log('=' .repeat(50));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function advancedDatabaseCheck() {
  try {
    console.log('📊 1. فحص الاتصال العام...');
    
    // فحص الاتصال بجدول آخر أولاً
    const { data: profilesTest, error: profilesError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (profilesError) {
      console.error('❌ مشكلة في الاتصال:', profilesError.message);
      return false;
    }
    console.log('✅ الاتصال بقاعدة البيانات يعمل');
    
    console.log('\n🔑 2. فحص جدول rsa_keys بطرق مختلفة...');
    
    // الطريقة الأولى: فحص عادي
    console.log('   📋 الطريقة الأولى: select عادي...');
    const { data: normalSelect, error: normalError } = await supabase
      .from('rsa_keys')
      .select('*');
    
    if (normalError) {
      console.error('   ❌ خطأ في select عادي:', normalError.message);
    } else {
      console.log(`   ✅ النتيجة: ${normalSelect?.length || 0} مفاتيح`);
      if (normalSelect && normalSelect.length > 0) {
        normalSelect.forEach((key, index) => {
          console.log(`      ${index + 1}. ${key.key_type} - نشط: ${key.active} - ID: ${key.id}`);
        });
      }
    }
    
    // الطريقة الثانية: فحص بدون فلترة
    console.log('\n   📋 الطريقة الثانية: select بدون شروط...');
    const { data: allKeys, error: allError } = await supabase
      .from('rsa_keys')
      .select('id, key_type, active, created_at');
    
    if (allError) {
      console.error('   ❌ خطأ في select الثاني:', allError.message);
    } else {
      console.log(`   ✅ النتيجة: ${allKeys?.length || 0} مفاتيح`);
      if (allKeys && allKeys.length > 0) {
        allKeys.forEach((key, index) => {
          console.log(`      ${index + 1}. ${key.key_type} - نشط: ${key.active} - تاريخ: ${key.created_at}`);
        });
      }
    }
    
    // الطريقة الثالثة: فحص المفاتيح النشطة فقط
    console.log('\n   📋 الطريقة الثالثة: المفاتيح النشطة فقط...');
    const { data: activeKeys, error: activeError } = await supabase
      .from('rsa_keys')
      .select('*')
      .eq('active', true);
    
    if (activeError) {
      console.error('   ❌ خطأ في فحص المفاتيح النشطة:', activeError.message);
    } else {
      console.log(`   ✅ المفاتيح النشطة: ${activeKeys?.length || 0}`);
      if (activeKeys && activeKeys.length > 0) {
        activeKeys.forEach((key, index) => {
          console.log(`      ${index + 1}. ${key.key_type} - طول المفتاح: ${key.key_pem?.length || 0} حرف`);
        });
      }
    }
    
    console.log('\n🔐 3. فحص RLS (Row Level Security)...');
    
    // محاولة إدراج مفتاح تجريبي لفحص RLS
    const { error: insertTestError } = await supabase
      .from('rsa_keys')
      .insert({
        key_type: 'test',
        key_pem: 'test-key-for-rls-check',
        active: false
      });
    
    if (insertTestError) {
      if (insertTestError.message.includes('row-level security')) {
        console.log('   ⚠️ RLS مفعل - قد يمنع رؤية البيانات');
        console.log('   💡 هذا قد يفسر عدم ظهور المفاتيح في السكريبت');
      } else {
        console.log('   ℹ️ خطأ آخر في الإدراج:', insertTestError.message);
      }
    } else {
      console.log('   ✅ يمكن الإدراج - RLS يسمح بالعمليات');
      
      // حذف المفتاح التجريبي
      await supabase
        .from('rsa_keys')
        .delete()
        .eq('key_type', 'test');
    }
    
    console.log('\n📈 4. ملخص التشخيص...');
    
    if (normalSelect && normalSelect.length > 0) {
      console.log('✅ المفاتيح موجودة ويمكن قراءتها');
      console.log('✅ النظام يعمل بشكل صحيح');
      console.log('🎉 يمكنك الآن استخدام النظام بثقة');
    } else if (normalError && normalError.message.includes('row-level security')) {
      console.log('⚠️ المفاتيح موجودة لكن RLS يمنع قراءتها');
      console.log('💡 هذا طبيعي - النظام سيعمل بشكل صحيح');
      console.log('🔧 المفاتيح المحلية ستُستخدم كحل احتياطي');
    } else {
      console.log('❌ لم يتم العثور على مفاتيح');
      console.log('💡 تأكد من رفع المفاتيح في Supabase Dashboard');
    }
    
    console.log('\n🚀 الخطوات التالية:');
    console.log('   1. أعد تشغيل التطبيق: npm run dev');
    console.log('   2. اختبر فتح مستند للتوقيع');
    console.log('   3. تحقق من عدم ظهور خطأ 500');
    
    return true;
    
  } catch (error) {
    console.error('💥 خطأ عام في التشخيص:', error.message);
    return false;
  }
}

advancedDatabaseCheck();
