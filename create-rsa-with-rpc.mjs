import { createClient } from '@supabase/supabase-js';
import forge from 'node-forge';

console.log('🔑 إنشاء مفاتيح RSA باستخدام RPC...');
console.log('=' .repeat(50));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createRSAKeysWithRPC() {
  try {
    console.log('🔧 إنشاء زوج مفاتيح RSA...');
    
    // إنشاء زوج مفاتيح RSA
    const keypair = forge.pki.rsa.generateKeyPair({ bits: 2048 });
    
    // تحويل المفاتيح إلى تنسيق PEM
    const privateKeyPem = forge.pki.privateKeyToPem(keypair.privateKey);
    const publicKeyPem = forge.pki.publicKeyToPem(keypair.publicKey);
    
    console.log('✅ تم إنشاء المفاتيح بنجاح');
    
    // محاولة استخدام RPC function لإدراج المفاتيح
    console.log('🔄 محاولة حفظ المفاتيح باستخدام RPC...');
    
    const sqlCommand = `
      -- تعطيل المفاتيح النشطة الحالية
      UPDATE rsa_keys SET active = false WHERE active = true;
      
      -- إدراج المفتاح الخاص
      INSERT INTO rsa_keys (key_type, key_pem, active) 
      VALUES ('private', '${privateKeyPem.replace(/'/g, "''")}', true);
      
      -- إدراج المفتاح العام
      INSERT INTO rsa_keys (key_type, key_pem, active) 
      VALUES ('public', '${publicKeyPem.replace(/'/g, "''")}', true);
    `;
    
    const { data: rpcData, error: rpcError } = await supabase.rpc('exec_sql', {
      sql: sqlCommand
    });
    
    if (rpcError) {
      console.error('❌ خطأ في RPC:', rpcError.message);
      
      // إذا فشل RPC، جرب طريقة أخرى
      console.log('🔄 محاولة طريقة بديلة...');
      return await createKeysDirectly(privateKeyPem, publicKeyPem);
    }
    
    console.log('✅ تم حفظ المفاتيح باستخدام RPC بنجاح');
    
    // التحقق من المفاتيح المحفوظة
    await verifyKeys();
    
    return true;
    
  } catch (error) {
    console.error('💥 خطأ عام:', error.message);
    return false;
  }
}

async function createKeysDirectly(privateKeyPem, publicKeyPem) {
  console.log('🔄 محاولة الحفظ المباشر...');
  
  try {
    // محاولة تعطيل RLS مؤقتاً (لن تعمل مع anon key)
    console.log('⚠️ محاولة الحفظ بدون RLS...');
    
    // تجربة إدراج مباشر (سيفشل على الأرجح)
    const { error: directError } = await supabase
      .from('rsa_keys')
      .insert([
        { key_type: 'private', key_pem: privateKeyPem, active: true },
        { key_type: 'public', key_pem: publicKeyPem, active: true }
      ]);
    
    if (directError) {
      console.error('❌ فشل الحفظ المباشر:', directError.message);
      console.log('💡 الحلول المقترحة:');
      console.log('   1. الحصول على Service Role Key من Supabase Dashboard');
      console.log('   2. تسجيل دخول مستخدم مشرف');
      console.log('   3. تعديل RLS policies في Supabase');
      return false;
    }
    
    console.log('✅ تم الحفظ المباشر بنجاح');
    return true;
    
  } catch (error) {
    console.error('💥 خطأ في الحفظ المباشر:', error.message);
    return false;
  }
}

async function verifyKeys() {
  console.log('🔍 التحقق من المفاتيح المحفوظة...');
  
  const { data: savedKeys, error: checkError } = await supabase
    .from('rsa_keys')
    .select('id, key_type, active')
    .eq('active', true);
  
  if (checkError) {
    console.error('❌ خطأ في التحقق:', checkError.message);
  } else {
    console.log(`✅ تم العثور على ${savedKeys?.length || 0} مفاتيح نشطة`);
    savedKeys?.forEach(key => {
      console.log(`   - ${key.key_type}: ${key.id}`);
    });
  }
}

createRSAKeysWithRPC();
