# إصلاح مشكلة التوقيع الإلكتروني RSA

## المشكلة
كان النظام يواجه خطأ 500 Internal Server Error عند محاولة فتح المستندات للتوقيع بسبب مشاكل في نظام التوقيع الإلكتروني RSA.

## الأسباب المكتشفة

### 1. خطأ في خدمة RSA Key Service
**الملف:** `src/lib/services/rsaKeyService.js`
**المشكلة:** في السطر 87، كان يتم استخدام `privateKeyData.key_pem` بدلاً من `publicKeyData.key_pem` لتحويل المفتاح العام.

```javascript
// خطأ
const publicKey = forge.pki.publicKeyFromPem(privateKeyData.key_pem);

// صحيح
const publicKey = forge.pki.publicKeyFromPem(publicKeyData.key_pem);
```

### 2. عدم تزامن الدوال
**المشكلة:** كانت دوال التوقيع والتحقق تستدعي `initRSAKeys()` بشكل متزامن بينما تحتاج لاستدعاء قاعدة البيانات بشكل غير متزامن.

## الإصلاحات المطبقة

### 1. إصلاح خدمة RSA Key Service
- تم إصلاح الخطأ في السطر 87
- تم التأكد من استخدام المفتاح الصحيح لكل عملية

### 2. تحديث دوال التوقيع لتكون غير متزامنة
**الملفات المحدثة:**
- `src/lib/utils/signatureUtils.js`
- `src/routes/dashboard/signed-documents/[id]/+page.svelte`

**التغييرات:**
```javascript
// قبل
export function createSignature(...)
export function verifySignature(...)
function signHashWithRSA(...)
function verifySignatureWithRSA(...)

// بعد
export async function createSignature(...)
export async function verifySignature(...)
async function signHashWithRSA(...)
async function verifySignatureWithRSA(...)
```

### 3. تحديث استدعاءات الدوال
```javascript
// قبل
const signature = createSignature(...);
const verification = verifySignature(...);

// بعد
const signature = await createSignature(...);
const verification = await verifySignature(...);
```

### 4. إنشاء سكريبت تهيئة مفاتيح RSA
**ملف جديد:** `src/lib/scripts/initRSAKeys.js`
- يتحقق من وجود مفاتيح RSA في قاعدة البيانات
- ينشئ مفاتيح جديدة إذا لم تكن موجودة
- يمكن تشغيله يدوياً أو تلقائياً

### 5. ملف تهيئة التطبيق
**ملف جديد:** `src/lib/init/startup.js`
- يقوم بتهيئة مفاتيح RSA عند بدء التطبيق
- يتعامل مع الأخطاء بشكل آمن

## كيفية التشغيل

### 1. تهيئة مفاتيح RSA يدوياً
```bash
node src/lib/scripts/initRSAKeys.js
```

### 2. التحقق من وجود المفاتيح في قاعدة البيانات
تأكد من وجود جدول `rsa_keys` وأن به مفاتيح نشطة:

```sql
SELECT * FROM rsa_keys WHERE active = true;
```

### 3. إعادة تشغيل التطبيق
بعد تطبيق الإصلاحات، أعد تشغيل التطبيق لتطبيق التغييرات.

## التحقق من الإصلاح

1. **افتح مستند للتوقيع** - يجب ألا يظهر خطأ 500
2. **قم بتوقيع مستند** - يجب أن يعمل التوقيع بنجاح
3. **تحقق من التوقيع** - يجب أن يتم التحقق من صحة التوقيع

## ملاحظات مهمة

- تأكد من وجود جدول `rsa_keys` في قاعدة البيانات
- المفاتيح محفوظة بشكل آمن في قاعدة البيانات
- النظام يستخدم RSA-2048 مع SHA-256
- في حالة فشل تحميل المفاتيح من قاعدة البيانات، سيتم إنشاء مفاتيح محلية مؤقتة

## الملفات المعدلة

1. `src/lib/services/rsaKeyService.js` - إصلاح خطأ المفتاح العام
2. `src/lib/utils/signatureUtils.js` - تحديث الدوال لتكون غير متزامنة
3. `src/routes/dashboard/signed-documents/[id]/+page.svelte` - تحديث استدعاءات الدوال
4. `src/lib/scripts/initRSAKeys.js` - سكريبت تهيئة جديد
5. `src/lib/init/startup.js` - ملف تهيئة التطبيق
6. `RSA_SIGNATURE_FIX.md` - هذا الملف للتوثيق
