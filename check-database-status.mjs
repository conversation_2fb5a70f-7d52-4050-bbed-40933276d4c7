import { createClient } from '@supabase/supabase-js';

console.log('🔍 فحص حالة قاعدة البيانات...');
console.log('=' .repeat(50));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkDatabaseStatus() {
  try {
    console.log('📊 1. فحص جدول rsa_keys...');
    
    // فحص بنية الجدول
    const { data: allKeys, error: allError } = await supabase
      .from('rsa_keys')
      .select('*');
    
    if (allError) {
      console.error('❌ خطأ في الوصول للجدول:', allError.message);
      return false;
    }
    
    console.log(`✅ الجدول موجود ويحتوي على ${allKeys?.length || 0} مفاتيح`);
    
    if (allKeys && allKeys.length > 0) {
      console.log('\n📋 المفاتيح الموجودة:');
      allKeys.forEach((key, index) => {
        console.log(`   ${index + 1}. ${key.key_type} - نشط: ${key.active} - تاريخ الإنشاء: ${key.created_at}`);
      });
    }
    
    console.log('\n🔐 2. فحص المفاتيح النشطة...');
    const { data: activeKeys, error: activeError } = await supabase
      .from('rsa_keys')
      .select('*')
      .eq('active', true);
    
    if (activeError) {
      console.error('❌ خطأ في جلب المفاتيح النشطة:', activeError.message);
    } else {
      console.log(`✅ عدد المفاتيح النشطة: ${activeKeys?.length || 0}`);
    }
    
    console.log('\n👤 3. فحص حالة المصادقة...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('❌ خطأ في المصادقة:', authError.message);
      console.log('💡 تحتاج لتسجيل الدخول لإضافة مفاتيح جديدة');
      return false;
    } else if (user) {
      console.log(`✅ مسجل دخول: ${user.email}`);
      
      // فحص الملف الشخصي
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role, full_name')
        .eq('id', user.id)
        .single();
      
      if (profileError) {
        console.warn('⚠️ لا يوجد ملف شخصي:', profileError.message);
      } else {
        console.log(`👤 الدور: ${profile?.role || 'غير محدد'}`);
        console.log(`📝 الاسم: ${profile?.full_name || 'غير محدد'}`);
      }
      
      return true;
    } else {
      console.warn('⚠️ غير مسجل دخول');
      console.log('💡 تحتاج لتسجيل الدخول لإضافة مفاتيح جديدة');
      return false;
    }
    
  } catch (error) {
    console.error('💥 خطأ عام:', error.message);
    return false;
  }
}

checkDatabaseStatus();
