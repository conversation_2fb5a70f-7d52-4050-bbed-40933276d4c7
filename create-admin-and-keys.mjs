import { createClient } from '@supabase/supabase-js';
import forge from 'node-forge';

console.log('👤 إنشاء مستخدم مشرف ومفاتيح RSA...');
console.log('=' .repeat(50));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// بيانات المستخدم المشرف
const adminUser = {
  email: '<EMAIL>',
  password: 'Admin123!@#',
  full_name: 'مدير النظام',
  role: 'مشرف'
};

async function createAdminAndKeys() {
  try {
    console.log('👤 1. إنشاء مستخدم مشرف...');
    
    // محاولة تسجيل مستخدم جديد
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: adminUser.email,
      password: adminUser.password,
      options: {
        data: {
          full_name: adminUser.full_name,
          role: adminUser.role
        }
      }
    });
    
    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.log('ℹ️ المستخدم موجود بالفعل، محاولة تسجيل الدخول...');
        
        // محاولة تسجيل الدخول
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: adminUser.email,
          password: adminUser.password
        });
        
        if (signInError) {
          console.error('❌ فشل تسجيل الدخول:', signInError.message);
          return false;
        }
        
        console.log('✅ تم تسجيل الدخول بنجاح');
      } else {
        console.error('❌ فشل إنشاء المستخدم:', signUpError.message);
        return false;
      }
    } else {
      console.log('✅ تم إنشاء المستخدم بنجاح');
      console.log('📧 تحقق من البريد الإلكتروني لتأكيد الحساب');
    }
    
    console.log('👤 2. التحقق من حالة المصادقة...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('❌ المستخدم غير مسجل دخول:', userError?.message);
      return false;
    }
    
    console.log(`✅ المستخدم مسجل دخول: ${user.email}`);
    
    console.log('📋 3. إنشاء/تحديث الملف الشخصي...');
    
    // إنشاء أو تحديث الملف الشخصي
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        id: user.id,
        email: user.email,
        full_name: adminUser.full_name,
        role: adminUser.role,
        updated_at: new Date().toISOString()
      });
    
    if (profileError) {
      console.error('❌ خطأ في إنشاء الملف الشخصي:', profileError.message);
    } else {
      console.log('✅ تم إنشاء الملف الشخصي بنجاح');
    }
    
    console.log('🔑 4. إنشاء مفاتيح RSA...');
    
    // إنشاء زوج مفاتيح RSA
    const keypair = forge.pki.rsa.generateKeyPair({ bits: 2048 });
    const privateKeyPem = forge.pki.privateKeyToPem(keypair.privateKey);
    const publicKeyPem = forge.pki.publicKeyToPem(keypair.publicKey);
    
    console.log('✅ تم إنشاء المفاتيح بنجاح');
    
    console.log('💾 5. حفظ المفاتيح في قاعدة البيانات...');
    
    // تعطيل المفاتيح النشطة الحالية
    await supabase
      .from('rsa_keys')
      .update({ active: false })
      .eq('active', true);
    
    // حفظ المفتاح الخاص
    const { error: privateKeyError } = await supabase
      .from('rsa_keys')
      .insert({
        key_type: 'private',
        key_pem: privateKeyPem,
        active: true
      });
    
    if (privateKeyError) {
      console.error('❌ خطأ في حفظ المفتاح الخاص:', privateKeyError.message);
      return false;
    }
    
    // حفظ المفتاح العام
    const { error: publicKeyError } = await supabase
      .from('rsa_keys')
      .insert({
        key_type: 'public',
        key_pem: publicKeyPem,
        active: true
      });
    
    if (publicKeyError) {
      console.error('❌ خطأ في حفظ المفتاح العام:', publicKeyError.message);
      return false;
    }
    
    console.log('✅ تم حفظ المفاتيح بنجاح');
    
    console.log('🔍 6. التحقق من المفاتيح المحفوظة...');
    
    const { data: savedKeys, error: checkError } = await supabase
      .from('rsa_keys')
      .select('id, key_type, active')
      .eq('active', true);
    
    if (checkError) {
      console.error('❌ خطأ في التحقق:', checkError.message);
    } else {
      console.log(`✅ تم العثور على ${savedKeys?.length || 0} مفاتيح نشطة`);
      savedKeys?.forEach(key => {
        console.log(`   - ${key.key_type}: ${key.id}`);
      });
    }
    
    console.log('\n🎉 تم إنشاء المستخدم المشرف ومفاتيح RSA بنجاح!');
    console.log('\n📝 بيانات تسجيل الدخول:');
    console.log(`   البريد الإلكتروني: ${adminUser.email}`);
    console.log(`   كلمة المرور: ${adminUser.password}`);
    console.log('\n⚠️ احفظ هذه البيانات في مكان آمن!');
    
    return true;
    
  } catch (error) {
    console.error('💥 خطأ عام:', error.message);
    return false;
  }
}

createAdminAndKeys();
