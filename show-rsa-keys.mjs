import fs from 'fs';

console.log('🔑 عرض مفاتيح RSA للنسخ اليدوي...');
console.log('=' .repeat(60));

function showRSAKeys() {
  try {
    console.log('📁 فحص ملفات المفاتيح...');
    
    // فحص وجود الملفات
    const privateKeyPath = 'temp-keys/private-key.pem';
    const publicKeyPath = 'temp-keys/public-key.pem';
    
    if (!fs.existsSync(privateKeyPath)) {
      console.error('❌ ملف المفتاح الخاص غير موجود:', privateKeyPath);
      console.log('💡 قم بتشغيل: node create-keys-simple.mjs');
      return false;
    }
    
    if (!fs.existsSync(publicKeyPath)) {
      console.error('❌ ملف المفتاح العام غير موجود:', publicKeyPath);
      console.log('💡 قم بتشغيل: node create-keys-simple.mjs');
      return false;
    }
    
    console.log('✅ تم العثور على ملفات المفاتيح');
    
    // قراءة المفاتيح
    const privateKeyPem = fs.readFileSync(privateKeyPath, 'utf8');
    const publicKeyPem = fs.readFileSync(publicKeyPath, 'utf8');
    
    console.log('\n' + '=' .repeat(60));
    console.log('🔐 المفتاح الخاص (Private Key)');
    console.log('=' .repeat(60));
    console.log('📋 انسخ المحتوى التالي بالكامل:');
    console.log('\n' + '─'.repeat(40));
    console.log(privateKeyPem);
    console.log('─'.repeat(40));
    
    console.log('\n' + '=' .repeat(60));
    console.log('🔓 المفتاح العام (Public Key)');
    console.log('=' .repeat(60));
    console.log('📋 انسخ المحتوى التالي بالكامل:');
    console.log('\n' + '─'.repeat(40));
    console.log(publicKeyPem);
    console.log('─'.repeat(40));
    
    console.log('\n' + '=' .repeat(60));
    console.log('📊 معلومات المفاتيح');
    console.log('=' .repeat(60));
    console.log(`🔐 طول المفتاح الخاص: ${privateKeyPem.length} حرف`);
    console.log(`🔓 طول المفتاح العام: ${publicKeyPem.length} حرف`);
    console.log(`📅 تاريخ إنشاء الملفات:`);
    
    const privateStats = fs.statSync(privateKeyPath);
    const publicStats = fs.statSync(publicKeyPath);
    
    console.log(`   - المفتاح الخاص: ${privateStats.mtime.toLocaleString('ar-SA')}`);
    console.log(`   - المفتاح العام: ${publicStats.mtime.toLocaleString('ar-SA')}`);
    
    console.log('\n' + '=' .repeat(60));
    console.log('📝 تعليمات الإدراج في Supabase Dashboard');
    console.log('=' .repeat(60));
    
    console.log('\n🔗 1. افتح Supabase Dashboard:');
    console.log('   https://supabase.com/dashboard');
    
    console.log('\n📋 2. انتقل إلى Table Editor → rsa_keys');
    
    console.log('\n➕ 3. أدرج المفتاح الخاص:');
    console.log('   - اضغط "Insert" → "Insert row"');
    console.log('   - key_type: private');
    console.log('   - key_pem: انسخ المفتاح الخاص من أعلاه');
    console.log('   - active: true');
    console.log('   - اضغط "Save"');
    
    console.log('\n➕ 4. أدرج المفتاح العام:');
    console.log('   - اضغط "Insert" → "Insert row"');
    console.log('   - key_type: public');
    console.log('   - key_pem: انسخ المفتاح العام من أعلاه');
    console.log('   - active: true');
    console.log('   - اضغط "Save"');
    
    console.log('\n✅ 5. تحقق من النتيجة:');
    console.log('   - يجب أن ترى مفتاحين نشطين في الجدول');
    console.log('   - قم بتشغيل: node check-database-status.mjs');
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎯 SQL للإدراج المباشر (اختياري)');
    console.log('=' .repeat(60));
    
    // إنشاء SQL للإدراج المباشر
    const escapedPrivateKey = privateKeyPem.replace(/'/g, "''");
    const escapedPublicKey = publicKeyPem.replace(/'/g, "''");
    
    console.log('\n📝 يمكنك أيضاً استخدام SQL Editor في Supabase:');
    console.log('\n```sql');
    console.log('-- تعطيل المفاتيح النشطة الحالية');
    console.log('UPDATE rsa_keys SET active = false WHERE active = true;');
    console.log('');
    console.log('-- إدراج المفتاح الخاص');
    console.log(`INSERT INTO rsa_keys (key_type, key_pem, active) VALUES ('private', '${escapedPrivateKey}', true);`);
    console.log('');
    console.log('-- إدراج المفتاح العام');
    console.log(`INSERT INTO rsa_keys (key_type, key_pem, active) VALUES ('public', '${escapedPublicKey}', true);`);
    console.log('```');
    
    console.log('\n🎉 بعد الإدراج، أعد تشغيل التطبيق لاستخدام مفاتيح قاعدة البيانات!');
    
    return true;
    
  } catch (error) {
    console.error('💥 خطأ في عرض المفاتيح:', error.message);
    return false;
  }
}

showRSAKeys();
