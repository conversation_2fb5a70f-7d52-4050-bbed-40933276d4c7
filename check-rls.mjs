import { createClient } from '@supabase/supabase-js';

console.log('🔍 فحص إعدادات RLS...');

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkRLS() {
  try {
    console.log('📋 محاولة قراءة جدول rsa_keys...');
    const { data: readData, error: readError } = await supabase
      .from('rsa_keys')
      .select('*')
      .limit(1);
    
    if (readError) {
      console.error('❌ خطأ في القراءة:', readError.message);
    } else {
      console.log('✅ يمكن قراءة الجدول');
    }

    console.log('✏️ محاولة إدراج بيانات تجريبية...');
    const { error: insertError } = await supabase
      .from('rsa_keys')
      .insert({
        key_type: 'test',
        key_pem: 'test-key',
        active: false
      });
    
    if (insertError) {
      console.error('❌ خطأ في الإدراج:', insertError.message);
      if (insertError.message.includes('row-level security')) {
        console.log('🔒 المشكلة: RLS يمنع الإدراج');
        console.log('💡 الحلول الممكنة:');
        console.log('   1. استخدام Service Role Key');
        console.log('   2. تسجيل دخول مستخدم مصرح له');
        console.log('   3. تعديل RLS policies');
      }
    } else {
      console.log('✅ يمكن إدراج البيانات');
    }

  } catch (error) {
    console.error('💥 خطأ عام:', error.message);
  }
}

checkRLS();
