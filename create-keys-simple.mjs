import { createClient } from '@supabase/supabase-js';
import forge from 'node-forge';

console.log('🔑 إنشاء مفاتيح RSA (طريقة مبسطة)...');
console.log('=' .repeat(50));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createKeysLocally() {
  try {
    console.log('🔧 إنشاء مفاتيح RSA محلياً...');
    
    // إنشاء زوج مفاتيح RSA
    const keypair = forge.pki.rsa.generateKeyPair({ bits: 2048 });
    const privateKeyPem = forge.pki.privateKeyToPem(keypair.privateKey);
    const publicKeyPem = forge.pki.publicKeyToPem(keypair.publicKey);
    
    console.log('✅ تم إنشاء المفاتيح بنجاح');
    
    console.log('💾 حفظ المفاتيح في ملفات محلية...');
    
    // حفظ المفاتيح في ملفات محلية كحل مؤقت
    const fs = await import('fs');
    
    // إنشاء مجلد للمفاتيح
    if (!fs.existsSync('temp-keys')) {
      fs.mkdirSync('temp-keys');
    }
    
    // حفظ المفاتيح
    fs.writeFileSync('temp-keys/private-key.pem', privateKeyPem);
    fs.writeFileSync('temp-keys/public-key.pem', publicKeyPem);
    
    console.log('✅ تم حفظ المفاتيح في مجلد temp-keys/');
    
    console.log('🔧 إنشاء ملف تكوين للمفاتيح...');
    
    // إنشاء ملف تكوين JavaScript
    const configContent = `// مفاتيح RSA المؤقتة
export const RSA_KEYS = {
  privateKey: \`${privateKeyPem}\`,
  publicKey: \`${publicKeyPem}\`
};

// استخدم هذه المفاتيح في حالة عدم توفر مفاتيح من قاعدة البيانات
export function getLocalRSAKeys() {
  const forge = require('node-forge');
  
  return {
    privateKey: RSA_KEYS.privateKey,
    publicKey: RSA_KEYS.publicKey,
    privateKeyObj: forge.pki.privateKeyFromPem(RSA_KEYS.privateKey),
    publicKeyObj: forge.pki.publicKeyFromPem(RSA_KEYS.publicKey)
  };
}
`;
    
    fs.writeFileSync('temp-keys/rsa-config.js', configContent);
    
    console.log('✅ تم إنشاء ملف التكوين');
    
    console.log('\n🎯 الخطوات التالية:');
    console.log('1. استخدم المفاتيح المحلية مؤقتاً');
    console.log('2. قم بتسجيل دخول مستخدم مشرف في التطبيق');
    console.log('3. استخدم واجهة التطبيق لإنشاء مفاتيح في قاعدة البيانات');
    
    console.log('\n📁 الملفات المنشأة:');
    console.log('   - temp-keys/private-key.pem');
    console.log('   - temp-keys/public-key.pem');
    console.log('   - temp-keys/rsa-config.js');
    
    return true;
    
  } catch (error) {
    console.error('💥 خطأ:', error.message);
    return false;
  }
}

createKeysLocally();
