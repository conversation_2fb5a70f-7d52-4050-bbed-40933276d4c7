import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

console.log('🔑 رفع مفاتيح RSA إلى قاعدة البيانات...');
console.log('=' .repeat(50));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function uploadRSAToDatabase() {
  try {
    console.log('👤 فحص حالة المصادقة...');
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('❌ المستخدم غير مسجل دخول');
      console.log('\n💡 لرفع المفاتيح إلى قاعدة البيانات، تحتاج إلى:');
      console.log('   1. فتح التطبيق في المتصفح: http://localhost:5175/');
      console.log('   2. تسجيل دخول أو إنشاء حساب جديد');
      console.log('   3. التأكد من أن لديك صلاحيات مشرف');
      console.log('   4. ثم تشغيل هذا السكريبت مرة أخرى');
      console.log('\n🔧 أو يمكنك استخدام الطريقة اليدوية:');
      console.log('   1. انسخ محتوى ملفات temp-keys/');
      console.log('   2. استخدم Supabase Dashboard');
      console.log('   3. أدرج المفاتيح يدوياً في جدول rsa_keys');
      return false;
    }
    
    console.log(`✅ مسجل دخول: ${user.email}`);
    
    console.log('\n📁 قراءة المفاتيح المحلية...');
    
    // قراءة المفاتيح من الملفات المحلية
    if (!fs.existsSync('temp-keys/private-key.pem') || !fs.existsSync('temp-keys/public-key.pem')) {
      console.error('❌ ملفات المفاتيح غير موجودة');
      console.log('💡 قم بتشغيل: node create-keys-simple.mjs');
      return false;
    }
    
    const privateKeyPem = fs.readFileSync('temp-keys/private-key.pem', 'utf8');
    const publicKeyPem = fs.readFileSync('temp-keys/public-key.pem', 'utf8');
    
    console.log('✅ تم قراءة المفاتيح من الملفات المحلية');
    
    console.log('\n🔄 تعطيل المفاتيح النشطة الحالية...');
    
    // تعطيل المفاتيح النشطة الحالية
    const { error: updateError } = await supabase
      .from('rsa_keys')
      .update({ active: false })
      .eq('active', true);
    
    if (updateError) {
      console.warn('⚠️ تحذير في تعطيل المفاتيح الحالية:', updateError.message);
    } else {
      console.log('✅ تم تعطيل المفاتيح النشطة الحالية');
    }
    
    console.log('\n💾 رفع المفتاح الخاص...');
    
    // رفع المفتاح الخاص
    const { data: privateKeyData, error: privateKeyError } = await supabase
      .from('rsa_keys')
      .insert({
        key_type: 'private',
        key_pem: privateKeyPem,
        active: true
      })
      .select();
    
    if (privateKeyError) {
      console.error('❌ خطأ في رفع المفتاح الخاص:', privateKeyError.message);
      
      if (privateKeyError.message.includes('row-level security')) {
        console.log('\n🔒 مشكلة في Row Level Security');
        console.log('💡 الحلول الممكنة:');
        console.log('   1. تأكد من أن لديك دور مشرف في الملف الشخصي');
        console.log('   2. استخدم Supabase Dashboard لإدراج المفاتيح يدوياً');
        console.log('   3. اطلب من مطور النظام تعديل RLS policies');
      }
      
      return false;
    }
    
    console.log('✅ تم رفع المفتاح الخاص بنجاح');
    
    console.log('\n💾 رفع المفتاح العام...');
    
    // رفع المفتاح العام
    const { data: publicKeyData, error: publicKeyError } = await supabase
      .from('rsa_keys')
      .insert({
        key_type: 'public',
        key_pem: publicKeyPem,
        active: true
      })
      .select();
    
    if (publicKeyError) {
      console.error('❌ خطأ في رفع المفتاح العام:', publicKeyError.message);
      return false;
    }
    
    console.log('✅ تم رفع المفتاح العام بنجاح');
    
    console.log('\n🔍 التحقق من المفاتيح المرفوعة...');
    
    // التحقق من المفاتيح المرفوعة
    const { data: uploadedKeys, error: checkError } = await supabase
      .from('rsa_keys')
      .select('id, key_type, active, created_at')
      .eq('active', true);
    
    if (checkError) {
      console.error('❌ خطأ في التحقق:', checkError.message);
    } else {
      console.log(`✅ تم العثور على ${uploadedKeys?.length || 0} مفاتيح نشطة في قاعدة البيانات`);
      
      if (uploadedKeys && uploadedKeys.length > 0) {
        console.log('\n📋 المفاتيح المرفوعة:');
        uploadedKeys.forEach((key, index) => {
          console.log(`   ${index + 1}. ${key.key_type} - ${key.id} - ${key.created_at}`);
        });
      }
    }
    
    console.log('\n🎉 تم رفع مفاتيح RSA إلى قاعدة البيانات بنجاح!');
    console.log('\n💡 الخطوات التالية:');
    console.log('   1. أعد تشغيل التطبيق لاستخدام المفاتيح من قاعدة البيانات');
    console.log('   2. احذف مجلد temp-keys/ إذا أردت (اختياري)');
    console.log('   3. اختبر نظام التوقيع الإلكتروني');
    
    return true;
    
  } catch (error) {
    console.error('💥 خطأ عام:', error.message);
    return false;
  }
}

uploadRSAToDatabase();
