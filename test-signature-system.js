#!/usr/bin/env node

/**
 * سكريبت اختبار بسيط لنظام التوقيع الإلكتروني
 */

import { supabase } from './src/lib/supabase.js';

async function testSignatureSystem() {
  console.log('🧪 اختبار نظام التوقيع الإلكتروني...');
  console.log('=' .repeat(50));
  
  try {
    // 1. اختبار الاتصال بقاعدة البيانات
    console.log('\n📊 1. اختبار الاتصال بقاعدة البيانات...');
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (testError) {
      console.error('❌ فشل الاتصال بقاعدة البيانات:', testError.message);
      return;
    }
    console.log('✅ الاتصال بقاعدة البيانات يعمل');

    // 2. اختبار جدول signed_documents
    console.log('\n📋 2. اختبار جدول signed_documents...');
    const { data: signedDocs, error: signedError } = await supabase
      .from('signed_documents')
      .select('id, status')
      .limit(5);
    
    if (signedError) {
      console.error('❌ خطأ في جدول signed_documents:', signedError.message);
    } else {
      console.log(`✅ جدول signed_documents يعمل (${signedDocs?.length || 0} مستندات)`);
    }

    // 3. اختبار جدول documents
    console.log('\n📄 3. اختبار جدول documents...');
    const { data: docs, error: docsError } = await supabase
      .from('documents')
      .select('id, title')
      .limit(5);
    
    if (docsError) {
      console.error('❌ خطأ في جدول documents:', docsError.message);
    } else {
      console.log(`✅ جدول documents يعمل (${docs?.length || 0} مستندات)`);
    }

    // 4. اختبار جدول rsa_keys
    console.log('\n🔑 4. اختبار جدول rsa_keys...');
    const { data: rsaKeys, error: rsaError } = await supabase
      .from('rsa_keys')
      .select('id, key_type, active')
      .eq('active', true);
    
    if (rsaError) {
      console.error('❌ خطأ في جدول rsa_keys:', rsaError.message);
      console.log('💡 قم بتشغيل: npm run init-rsa');
    } else {
      console.log(`✅ جدول rsa_keys يعمل (${rsaKeys?.length || 0} مفاتيح نشطة)`);
      if (rsaKeys && rsaKeys.length === 0) {
        console.warn('⚠️ لا توجد مفاتيح RSA نشطة');
        console.log('💡 قم بتشغيل: npm run init-rsa');
      }
    }

    // 5. اختبار المصادقة
    console.log('\n🔐 5. اختبار المصادقة...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('❌ خطأ في المصادقة:', authError.message);
    } else if (user) {
      console.log(`✅ المستخدم مسجل دخول: ${user.email}`);
    } else {
      console.warn('⚠️ لا يوجد مستخدم مسجل دخول');
    }

    console.log('\n' + '=' .repeat(50));
    console.log('🎯 نتائج الاختبار:');
    console.log('- إذا كانت جميع الاختبارات ✅، فالنظام يعمل بشكل صحيح');
    console.log('- إذا كان هناك ❌، راجع الأخطاء أعلاه');
    console.log('- إذا كان هناك ⚠️، قد تحتاج لإجراءات إضافية');

  } catch (error) {
    console.error('💥 خطأ عام في الاختبار:', error);
  }
}

// تشغيل الاختبار
testSignatureSystem();
