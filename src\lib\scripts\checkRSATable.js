/**
 * سكريبت للتحقق من وجود جدول rsa_keys وإنشاؤه إذا لم يكن موجوداً
 */

import { supabase } from '../supabase.js';

async function checkAndCreateRSATable() {
  try {
    console.log('🔍 التحقق من وجود جدول rsa_keys...');
    
    // محاولة الاستعلام عن الجدول
    const { data, error } = await supabase
      .from('rsa_keys')
      .select('id')
      .limit(1);
    
    if (error) {
      if (error.message.includes('does not exist') || error.code === '42P01') {
        console.log('⚠️ جدول rsa_keys غير موجود، سيتم إنشاؤه...');
        await createRSATable();
      } else {
        console.error('❌ خطأ في الاستعلام عن جدول rsa_keys:', error);
        throw error;
      }
    } else {
      console.log('✅ جدول rsa_keys موجود');
      console.log(`📊 عدد المفاتيح الموجودة: ${data?.length || 0}`);
    }
  } catch (error) {
    console.error('💥 خطأ في التحقق من جدول rsa_keys:', error);
    throw error;
  }
}

async function createRSATable() {
  try {
    console.log('🔧 إنشاء جدول rsa_keys...');
    
    // استخدام RPC لتنفيذ SQL مباشرة
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- إنشاء جدول مفاتيح RSA
        CREATE TABLE IF NOT EXISTS public.rsa_keys (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          key_type TEXT NOT NULL CHECK (key_type IN ('public', 'private')),
          key_pem TEXT NOT NULL,
          active BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- إنشاء فهرس جزئي للمفاتيح النشطة لتحسين الأداء
        CREATE INDEX IF NOT EXISTS idx_rsa_keys_active ON public.rsa_keys (key_type) WHERE active = TRUE;

        -- إنشاء trigger لتحديث updated_at تلقائياً
        CREATE OR REPLACE FUNCTION update_rsa_keys_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE TRIGGER trigger_update_rsa_keys_updated_at
          BEFORE UPDATE ON public.rsa_keys
          FOR EACH ROW
          EXECUTE FUNCTION update_rsa_keys_updated_at();

        -- تفعيل Row Level Security
        ALTER TABLE public.rsa_keys ENABLE ROW LEVEL SECURITY;

        -- سياسة القراءة: يمكن للمستخدمين المصرح لهم قراءة المفاتيح
        CREATE POLICY rsa_keys_select_policy ON public.rsa_keys
        FOR SELECT USING (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('مشرف', 'مدير', 'admin', 'manager')
          )
        );

        -- سياسة الإنشاء والتحديث: يمكن للمشرفين فقط إنشاء وتحديث المفاتيح
        CREATE POLICY rsa_keys_insert_policy ON public.rsa_keys
        FOR INSERT WITH CHECK (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('مشرف', 'مدير', 'admin', 'manager')
          )
        );

        CREATE POLICY rsa_keys_update_policy ON public.rsa_keys
        FOR UPDATE USING (
          EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role IN ('مشرف', 'مدير', 'admin', 'manager')
          )
        );

        -- إضافة تعليقات للجدول والأعمدة
        COMMENT ON TABLE public.rsa_keys IS 'جدول مفاتيح RSA للتوقيع الإلكتروني';
        COMMENT ON COLUMN public.rsa_keys.id IS 'معرف فريد للمفتاح';
        COMMENT ON COLUMN public.rsa_keys.key_type IS 'نوع المفتاح (عام أو خاص)';
        COMMENT ON COLUMN public.rsa_keys.key_pem IS 'المفتاح بتنسيق PEM';
        COMMENT ON COLUMN public.rsa_keys.active IS 'حالة المفتاح (نشط أم لا)';

        -- منح الصلاحيات للمستخدمين المصرح لهم
        GRANT SELECT, INSERT, UPDATE ON public.rsa_keys TO authenticated;
      `
    });
    
    if (error) {
      console.error('❌ خطأ في إنشاء جدول rsa_keys:', error);
      throw error;
    }
    
    console.log('✅ تم إنشاء جدول rsa_keys بنجاح');
  } catch (error) {
    console.error('💥 فشل في إنشاء جدول rsa_keys:', error);
    throw error;
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (import.meta.url === `file://${process.argv[1]}`) {
  checkAndCreateRSATable()
    .then(() => {
      console.log('🎉 تم التحقق من جدول rsa_keys بنجاح');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في التحقق من جدول rsa_keys:', error);
      process.exit(1);
    });
}

export { checkAndCreateRSATable };
