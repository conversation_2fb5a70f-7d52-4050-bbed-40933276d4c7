# دليل الإصلاح السريع لمشكلة التوقيع الإلكتروني

## المشكلة
خطأ 500 Internal Server Error عند فتح المستندات للتوقيع.

## الحل السريع

### الطريقة الأولى: تشغيل سكريبت الإصلاح الشامل
```bash
npm run fix-signature
```

### الطريقة الثانية: خطوة بخطوة
```bash
# 1. التحقق من جدول rsa_keys
npm run check-rsa-table

# 2. تهيئة مفاتيح RSA
npm run init-rsa
```

### الطريقة الثالثة: يدوياً
```bash
# تشغيل السكريبت مباشرة
node fix-signature-system.js
```

## بعد تطبيق الإصلاح

1. **أعد تشغيل التطبيق**
   ```bash
   npm run dev
   ```

2. **اختبر النظام**
   - افتح مستند للتوقيع
   - تأكد من عدم ظهور خطأ 500
   - جرب التوقيع والتحقق

## إذا استمرت المشكلة

### تحقق من قاعدة البيانات
```sql
-- تحقق من وجود جدول rsa_keys
SELECT * FROM rsa_keys WHERE active = true;

-- إذا كان الجدول فارغ، قم بتشغيل:
npm run init-rsa
```

### تحقق من الأخطاء في وحدة التحكم
- افتح Developer Tools في المتصفح
- تحقق من تبويب Console للأخطاء
- تحقق من تبويب Network للطلبات الفاشلة

### تحقق من ملفات السجل
- تحقق من سجلات الخادم
- ابحث عن أخطاء متعلقة بـ RSA أو التوقيع

## الملفات المهمة المعدلة

- ✅ `src/lib/services/rsaKeyService.js` - إصلاح خطأ المفتاح العام
- ✅ `src/lib/utils/signatureUtils.js` - تحديث الدوال لتكون غير متزامنة
- ✅ `src/routes/dashboard/signed-documents/[id]/+page.svelte` - تحديث استدعاءات الدوال
- ✅ `fix-signature-system.js` - سكريبت الإصلاح الشامل
- ✅ `package.json` - إضافة سكريبتات جديدة

## للمطورين

إذا كنت تعمل على تطوير النظام:

1. **تأكد من تهيئة مفاتيح RSA عند بدء التطبيق**
2. **استخدم `await` مع دوال التوقيع والتحقق**
3. **تحقق من وجود جدول `rsa_keys` في قاعدة البيانات**

## الدعم

إذا واجهت مشاكل أخرى، تحقق من:
- اتصال قاعدة البيانات
- صلاحيات المستخدم في Supabase
- إعدادات RLS (Row Level Security)
- مكتبات التشفير (node-forge, crypto-js)
