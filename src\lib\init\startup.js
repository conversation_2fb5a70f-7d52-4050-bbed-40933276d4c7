/**
 * ملف تهيئة التطبيق عند البدء
 */

import { initializeRSAKeys } from '../scripts/initRSAKeys.js';

/**
 * تهيئة التطبيق عند البدء
 */
export async function initializeApp() {
  try {
    console.log('🚀 بدء تهيئة التطبيق...');
    
    // تهيئة مفاتيح RSA
    await initializeRSAKeys();
    
    console.log('✅ تمت تهيئة التطبيق بنجاح');
  } catch (error) {
    console.error('❌ خطأ في تهيئة التطبيق:', error);
    // لا نوقف التطبيق في حالة فشل التهيئة
  }
}

// تشغيل التهيئة تلقائياً عند استيراد الملف
if (typeof window === 'undefined') {
  // تشغيل فقط في بيئة الخادم
  initializeApp();
}
