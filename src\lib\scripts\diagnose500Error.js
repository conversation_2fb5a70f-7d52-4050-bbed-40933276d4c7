/**
 * سكريبت تشخيص خطأ 500 في نظام التوقيع الإلكتروني
 */

import { supabase } from '../supabase.js';
import { RSAKeyService } from '../services/rsaKeyService.js';

async function diagnose500Error() {
  console.log('🔍 بدء تشخيص خطأ 500...');
  console.log('=' .repeat(60));
  
  const results = {
    database: { status: 'unknown', details: [] },
    rsaKeys: { status: 'unknown', details: [] },
    tables: { status: 'unknown', details: [] },
    permissions: { status: 'unknown', details: [] },
    storage: { status: 'unknown', details: [] }
  };

  try {
    // 1. اختبار الاتصال بقاعدة البيانات
    console.log('\n📊 1. اختبار الاتصال بقاعدة البيانات...');
    try {
      const { data, error } = await supabase.from('profiles').select('id').limit(1);
      if (error) {
        results.database.status = 'error';
        results.database.details.push(`خطأ في الاتصال: ${error.message}`);
        console.error('❌ فشل الاتصال بقاعدة البيانات:', error.message);
      } else {
        results.database.status = 'success';
        results.database.details.push('الاتصال بقاعدة البيانات يعمل بشكل صحيح');
        console.log('✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح');
      }
    } catch (dbError) {
      results.database.status = 'error';
      results.database.details.push(`استثناء في الاتصال: ${dbError.message}`);
      console.error('💥 استثناء في الاتصال بقاعدة البيانات:', dbError);
    }

    // 2. فحص الجداول المطلوبة
    console.log('\n📋 2. فحص الجداول المطلوبة...');
    const requiredTables = ['signed_documents', 'documents', 'rsa_keys', 'profiles'];
    
    for (const table of requiredTables) {
      try {
        const { data, error } = await supabase.from(table).select('*').limit(1);
        if (error) {
          results.tables.status = 'error';
          results.tables.details.push(`جدول ${table}: خطأ - ${error.message}`);
          console.error(`❌ جدول ${table}: ${error.message}`);
        } else {
          results.tables.details.push(`جدول ${table}: موجود ويعمل`);
          console.log(`✅ جدول ${table}: موجود ويعمل`);
        }
      } catch (tableError) {
        results.tables.status = 'error';
        results.tables.details.push(`جدول ${table}: استثناء - ${tableError.message}`);
        console.error(`💥 جدول ${table}: استثناء -`, tableError);
      }
    }
    
    if (results.tables.status !== 'error') {
      results.tables.status = 'success';
    }

    // 3. فحص مفاتيح RSA
    console.log('\n🔑 3. فحص مفاتيح RSA...');
    try {
      const keys = await RSAKeyService.getActiveKeys();
      if (keys && keys.privateKeyObj && keys.publicKeyObj) {
        results.rsaKeys.status = 'success';
        results.rsaKeys.details.push('مفاتيح RSA موجودة وصالحة');
        console.log('✅ مفاتيح RSA موجودة وصالحة');
      } else {
        results.rsaKeys.status = 'warning';
        results.rsaKeys.details.push('مفاتيح RSA غير موجودة أو غير صالحة');
        console.warn('⚠️ مفاتيح RSA غير موجودة أو غير صالحة');
      }
    } catch (rsaError) {
      results.rsaKeys.status = 'error';
      results.rsaKeys.details.push(`خطأ في مفاتيح RSA: ${rsaError.message}`);
      console.error('❌ خطأ في مفاتيح RSA:', rsaError);
    }

    // 4. فحص الصلاحيات
    console.log('\n🔐 4. فحص الصلاحيات...');
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        results.permissions.details.push(`المستخدم مسجل دخول: ${user.email}`);
        console.log(`✅ المستخدم مسجل دخول: ${user.email}`);
        
        // فحص الملف الشخصي
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('role, unit_id')
          .eq('id', user.id)
          .single();
          
        if (profileError) {
          results.permissions.status = 'error';
          results.permissions.details.push(`خطأ في الملف الشخصي: ${profileError.message}`);
          console.error('❌ خطأ في الملف الشخصي:', profileError);
        } else {
          results.permissions.status = 'success';
          results.permissions.details.push(`الدور: ${profile?.role || 'غير محدد'}`);
          results.permissions.details.push(`الوحدة: ${profile?.unit_id || 'غير محدد'}`);
          console.log(`✅ الدور: ${profile?.role || 'غير محدد'}`);
          console.log(`✅ الوحدة: ${profile?.unit_id || 'غير محدد'}`);
        }
      } else {
        results.permissions.status = 'warning';
        results.permissions.details.push('المستخدم غير مسجل دخول');
        console.warn('⚠️ المستخدم غير مسجل دخول');
      }
    } catch (permError) {
      results.permissions.status = 'error';
      results.permissions.details.push(`خطأ في الصلاحيات: ${permError.message}`);
      console.error('❌ خطأ في الصلاحيات:', permError);
    }

    // 5. فحص Storage
    console.log('\n💾 5. فحص Storage...');
    try {
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
      if (bucketsError) {
        results.storage.status = 'error';
        results.storage.details.push(`خطأ في Storage: ${bucketsError.message}`);
        console.error('❌ خطأ في Storage:', bucketsError);
      } else {
        results.storage.status = 'success';
        results.storage.details.push(`عدد Buckets: ${buckets?.length || 0}`);
        console.log(`✅ Storage يعمل، عدد Buckets: ${buckets?.length || 0}`);
      }
    } catch (storageError) {
      results.storage.status = 'error';
      results.storage.details.push(`استثناء في Storage: ${storageError.message}`);
      console.error('💥 استثناء في Storage:', storageError);
    }

  } catch (generalError) {
    console.error('💥 خطأ عام في التشخيص:', generalError);
  }

  // طباعة التقرير النهائي
  console.log('\n' + '=' .repeat(60));
  console.log('📋 تقرير التشخيص النهائي:');
  console.log('=' .repeat(60));
  
  for (const [component, result] of Object.entries(results)) {
    const statusIcon = result.status === 'success' ? '✅' : 
                      result.status === 'warning' ? '⚠️' : '❌';
    console.log(`\n${statusIcon} ${component.toUpperCase()}:`);
    result.details.forEach(detail => console.log(`   - ${detail}`));
  }

  // تحديد المشاكل الحرجة
  const criticalIssues = [];
  if (results.database.status === 'error') criticalIssues.push('قاعدة البيانات');
  if (results.tables.status === 'error') criticalIssues.push('الجداول');
  if (results.rsaKeys.status === 'error') criticalIssues.push('مفاتيح RSA');

  console.log('\n' + '=' .repeat(60));
  if (criticalIssues.length > 0) {
    console.log('🚨 مشاكل حرجة تحتاج إصلاح:');
    criticalIssues.forEach(issue => console.log(`   - ${issue}`));
    
    console.log('\n💡 اقتراحات الإصلاح:');
    if (criticalIssues.includes('قاعدة البيانات')) {
      console.log('   - تحقق من إعدادات Supabase');
      console.log('   - تأكد من صحة URL و API Keys');
    }
    if (criticalIssues.includes('الجداول')) {
      console.log('   - قم بتشغيل migrations قاعدة البيانات');
      console.log('   - تحقق من صلاحيات RLS');
    }
    if (criticalIssues.includes('مفاتيح RSA')) {
      console.log('   - قم بتشغيل: npm run init-rsa');
      console.log('   - تحقق من جدول rsa_keys');
    }
  } else {
    console.log('🎉 لا توجد مشاكل حرجة واضحة!');
    console.log('💭 قد تكون المشكلة في:');
    console.log('   - كود JavaScript/TypeScript');
    console.log('   - إعدادات SvelteKit');
    console.log('   - مشاكل في المتصفح');
  }

  return results;
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (import.meta.url === `file://${process.argv[1]}`) {
  diagnose500Error()
    .then(() => {
      console.log('\n🏁 انتهى التشخيص');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل التشخيص:', error);
      process.exit(1);
    });
}

export { diagnose500Error };
