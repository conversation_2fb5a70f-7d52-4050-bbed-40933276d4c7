import { createClient } from '@supabase/supabase-js';

console.log('🧪 اختبار النظام المحدث مع المفاتيح المحلية...');
console.log('=' .repeat(60));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testUpdatedSystem() {
  try {
    console.log('📊 1. اختبار الاتصال بقاعدة البيانات...');
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (testError) {
      console.error('❌ فشل الاتصال:', testError.message);
      return false;
    }
    console.log('✅ الاتصال يعمل');

    console.log('🔑 2. اختبار المفاتيح المحلية...');
    
    // محاكاة استيراد LocalRSAService (لا يمكن استيراده مباشرة هنا)
    console.log('   - فحص ملفات المفاتيح المحلية...');
    
    // فحص وجود الملفات
    const fs = await import('fs');
    const localKeysExist = fs.existsSync('temp-keys/private-key.pem') && 
                          fs.existsSync('temp-keys/public-key.pem');
    
    if (localKeysExist) {
      console.log('✅ ملفات المفاتيح المحلية موجودة');
    } else {
      console.error('❌ ملفات المفاتيح المحلية غير موجودة');
      return false;
    }

    console.log('🔑 3. اختبار جدول rsa_keys...');
    const { data: rsaKeys, error: rsaError } = await supabase
      .from('rsa_keys')
      .select('id, key_type, active')
      .eq('active', true);
    
    if (rsaError) {
      console.error('❌ خطأ في جدول rsa_keys:', rsaError.message);
    } else {
      console.log(`✅ جدول rsa_keys يعمل (${rsaKeys?.length || 0} مفاتيح نشطة)`);
      if (rsaKeys && rsaKeys.length === 0) {
        console.log('ℹ️ لا توجد مفاتيح في قاعدة البيانات - سيتم استخدام المفاتيح المحلية');
      }
    }

    console.log('📋 4. اختبار الجداول الأخرى...');
    
    // اختبار جدول signed_documents
    const { data: signedDocs, error: signedError } = await supabase
      .from('signed_documents')
      .select('id, status')
      .limit(3);
    
    if (signedError) {
      console.error('❌ خطأ في signed_documents:', signedError.message);
    } else {
      console.log(`✅ signed_documents يعمل (${signedDocs?.length || 0} مستندات)`);
    }

    // اختبار جدول documents
    const { data: docs, error: docsError } = await supabase
      .from('documents')
      .select('id, title')
      .limit(3);
    
    if (docsError) {
      console.error('❌ خطأ في documents:', docsError.message);
    } else {
      console.log(`✅ documents يعمل (${docs?.length || 0} مستندات)`);
    }

    console.log('🔐 5. اختبار المصادقة...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('❌ خطأ في المصادقة:', authError.message);
    } else if (user) {
      console.log(`✅ مستخدم مسجل: ${user.email}`);
    } else {
      console.warn('⚠️ لا يوجد مستخدم مسجل');
      console.log('💡 يمكنك تسجيل الدخول باستخدام:');
      console.log('   البريد الإلكتروني: <EMAIL>');
      console.log('   كلمة المرور: Admin123!@#');
    }

    console.log('\n' + '=' .repeat(60));
    console.log('🎯 ملخص حالة النظام:');
    console.log('✅ الاتصال بقاعدة البيانات: يعمل');
    console.log('✅ المفاتيح المحلية: متوفرة');
    console.log('✅ الجداول الأساسية: تعمل');
    console.log('⚠️ المصادقة: تحتاج تسجيل دخول');
    
    console.log('\n🚀 النظام جاهز للاستخدام!');
    console.log('💡 الخطوات التالية:');
    console.log('   1. قم بتشغيل التطبيق: npm run dev');
    console.log('   2. سجل دخول أو أنشئ حساب جديد');
    console.log('   3. جرب فتح مستند للتوقيع');
    
    return true;

  } catch (error) {
    console.error('💥 خطأ عام في الاختبار:', error.message);
    return false;
  }
}

testUpdatedSystem();
