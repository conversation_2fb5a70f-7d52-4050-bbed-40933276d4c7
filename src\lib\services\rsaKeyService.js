import { supabase } from '$lib/supabase';
import forge from 'node-forge';
import { LocalRSAService } from './localRSAService.js';

/**
 * خدمة إدارة مفاتيح RSA
 */
export class RSAKeyService {
  /**
   * إنشاء زوج مفاتيح RSA جديد وتخزينه في قاعدة البيانات
   * @returns {Promise<Object>} - زوج المفاتيح
   */
  static async generateAndStoreKeyPair() {
    try {
      // إنشاء زوج مفاتيح RSA
      const keypair = forge.pki.rsa.generateKeyPair({ bits: 2048 });

      // تحويل المفاتيح إلى تنسيق PEM
      const privateKeyPem = forge.pki.privateKeyToPem(keypair.privateKey);
      const publicKeyPem = forge.pki.publicKeyToPem(keypair.publicKey);

      // تعطيل المفاتيح النشطة الحالية
      await supabase
        .from('rsa_keys')
        .update({ active: false })
        .eq('active', true);

      // تخزين المفتاح الخاص
      const { error: privateKeyError } = await supabase
        .from('rsa_keys')
        .insert({
          key_type: 'private',
          key_pem: privateKeyPem,
          active: true
        });

      if (privateKeyError) throw privateKeyError;

      // تخزين المفتاح العام
      const { error: publicKeyError } = await supabase
        .from('rsa_keys')
        .insert({
          key_type: 'public',
          key_pem: publicKeyPem,
          active: true
        });

      if (publicKeyError) throw publicKeyError;

      return {
        privateKey: privateKeyPem,
        publicKey: publicKeyPem
      };
    } catch (error) {
      console.error('خطأ في إنشاء وتخزين مفاتيح RSA:', error);
      throw error;
    }
  }

  /**
   * الحصول على المفاتيح النشطة من قاعدة البيانات
   * @returns {Promise<Object>} - زوج المفاتيح
   */
  static async getActiveKeys() {
    try {
      // الحصول على المفتاح الخاص
      const { data: privateKeyData, error: privateKeyError } = await supabase
        .from('rsa_keys')
        .select('key_pem')
        .eq('key_type', 'private')
        .eq('active', true)
        .single();

      if (privateKeyError) throw privateKeyError;

      // الحصول على المفتاح العام
      const { data: publicKeyData, error: publicKeyError } = await supabase
        .from('rsa_keys')
        .select('key_pem')
        .eq('key_type', 'public')
        .eq('active', true)
        .single();

      if (publicKeyError) throw publicKeyError;

      // تحويل المفاتيح من تنسيق PEM إلى كائنات
      const privateKey = forge.pki.privateKeyFromPem(privateKeyData.key_pem);
      const publicKey = forge.pki.publicKeyFromPem(publicKeyData.key_pem);

      return {
        privateKey: privateKeyData.key_pem,
        publicKey: publicKeyData.key_pem,
        privateKeyObj: privateKey,
        publicKeyObj: publicKey
      };
    } catch (error) {
      console.error('خطأ في الحصول على مفاتيح RSA من قاعدة البيانات:', error);

      // استخدام المفاتيح المحلية كحل احتياطي
      console.log('⚠️ استخدام المفاتيح المحلية كحل احتياطي...');

      try {
        const localKeys = LocalRSAService.getLocalKeys();
        console.log('✅ تم تحميل المفاتيح المحلية بنجاح');
        return localKeys;
      } catch (localError) {
        console.error('❌ فشل في تحميل المفاتيح المحلية:', localError);
        throw new Error('لم يتم العثور على مفاتيح RSA من قاعدة البيانات أو الملفات المحلية');
      }
    }
  }
}