console.log('🧪 بدء الاختبار الأساسي...');

// اختبار بسيط جداً
try {
  console.log('✅ JavaScript يعمل');
  
  // اختبار require
  const fs = require('fs');
  console.log('✅ require يعمل');
  
  // اختبار async/await
  (async () => {
    console.log('✅ async/await يعمل');
    
    // اختبار تحميل مكتبة Supabase
    try {
      const { createClient } = require('@supabase/supabase-js');
      console.log('✅ مكتبة Supabase تم تحميلها');
      
      // إنشاء عميل Supabase
      const supabase = createClient(
        'https://ipbglrpzcziafodbyoqd.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80'
      );
      console.log('✅ عميل Supabase تم إنشاؤه');
      
      // اختبار الاتصال
      console.log('🔍 اختبار الاتصال...');
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);
      
      if (error) {
        console.error('❌ خطأ في الاتصال:', error.message);
      } else {
        console.log('✅ الاتصال بقاعدة البيانات يعمل');
      }
      
    } catch (supabaseError) {
      console.error('❌ خطأ في مكتبة Supabase:', supabaseError.message);
    }
    
  })();
  
} catch (error) {
  console.error('💥 خطأ عام:', error.message);
}
