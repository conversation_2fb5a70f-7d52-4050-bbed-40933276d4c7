import { createClient } from '@supabase/supabase-js';

console.log('🔍 تشخيص دقيق لخطأ 500...');
console.log('=' .repeat(50));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function diagnose500Error() {
  try {
    console.log('🔧 1. اختبار تحميل المفاتيح من قاعدة البيانات...');
    
    // محاكاة ما يحدث في RSAKeyService.getActiveKeys()
    const { data: privateKeyData, error: privateError } = await supabase
      .from('rsa_keys')
      .select('key_pem')
      .eq('key_type', 'private')
      .eq('active', true)
      .single();
    
    if (privateError) {
      console.error('❌ خطأ في جلب المفتاح الخاص:', privateError.message);
      return false;
    }
    
    const { data: publicKeyData, error: publicError } = await supabase
      .from('rsa_keys')
      .select('key_pem')
      .eq('key_type', 'public')
      .eq('active', true)
      .single();
    
    if (publicError) {
      console.error('❌ خطأ في جلب المفتاح العام:', publicError.message);
      return false;
    }
    
    console.log('✅ تم جلب المفاتيح من قاعدة البيانات');
    console.log(`   - طول المفتاح الخاص: ${privateKeyData.key_pem?.length || 0} حرف`);
    console.log(`   - طول المفتاح العام: ${publicKeyData.key_pem?.length || 0} حرف`);
    
    console.log('\n🔧 2. اختبار تحويل المفاتيح إلى كائنات...');
    
    // محاكاة ما يحدث في forge.pki
    try {
      const forge = await import('node-forge');
      
      console.log('   🔐 تحويل المفتاح الخاص...');
      const privateKey = forge.default.pki.privateKeyFromPem(privateKeyData.key_pem);
      console.log('   ✅ المفتاح الخاص تم تحويله بنجاح');
      
      console.log('   🔓 تحويل المفتاح العام...');
      const publicKey = forge.default.pki.publicKeyFromPem(publicKeyData.key_pem);
      console.log('   ✅ المفتاح العام تم تحويله بنجاح');
      
      console.log('\n🔧 3. اختبار التوقيع والتحقق...');
      
      // اختبار بسيط للتوقيع
      const testMessage = 'test-signature-message';
      const md = forge.default.md.sha256.create();
      md.update(testMessage, 'utf8');
      
      console.log('   ✍️ إنشاء توقيع تجريبي...');
      const signature = privateKey.sign(md);
      console.log('   ✅ تم إنشاء التوقيع');
      
      console.log('   🔍 التحقق من التوقيع...');
      const isValid = publicKey.verify(md.digest().bytes(), signature);
      console.log(`   ${isValid ? '✅' : '❌'} نتيجة التحقق: ${isValid}`);
      
      if (!isValid) {
        console.error('❌ المفاتيح لا تعمل بشكل صحيح!');
        return false;
      }
      
    } catch (forgeError) {
      console.error('❌ خطأ في مكتبة node-forge:', forgeError.message);
      console.error('📋 تفاصيل الخطأ:', forgeError.stack);
      return false;
    }
    
    console.log('\n🔧 4. اختبار محاكاة initRSAKeys...');
    
    try {
      // محاكاة دالة initRSAKeys
      console.log('   🔄 محاكاة تهيئة المفاتيح...');
      
      const keys = {
        privateKey: privateKeyData.key_pem,
        publicKey: publicKeyData.key_pem,
        privateKeyObj: forge.default.pki.privateKeyFromPem(privateKeyData.key_pem),
        publicKeyObj: forge.default.pki.publicKeyFromPem(publicKeyData.key_pem)
      };
      
      console.log('   ✅ تم إنشاء كائن المفاتيح بنجاح');
      
      // اختبار دالة التوقيع
      console.log('   ✍️ اختبار دالة التوقيع...');
      
      const documentContent = 'محتوى مستند تجريبي للتوقيع';
      const hash = forge.default.md.sha256.create();
      hash.update(documentContent, 'utf8');
      
      const testSignature = keys.privateKeyObj.sign(hash);
      const base64Signature = forge.default.util.encode64(testSignature);
      
      console.log('   ✅ تم إنشاء توقيع للمستند');
      console.log(`   📝 طول التوقيع: ${base64Signature.length} حرف`);
      
    } catch (initError) {
      console.error('❌ خطأ في محاكاة initRSAKeys:', initError.message);
      console.error('📋 تفاصيل الخطأ:', initError.stack);
      return false;
    }
    
    console.log('\n🔧 5. فحص المستندات الموجودة...');
    
    const { data: documents, error: docsError } = await supabase
      .from('documents')
      .select('id, title, content')
      .limit(3);
    
    if (docsError) {
      console.error('❌ خطأ في جلب المستندات:', docsError.message);
    } else {
      console.log(`✅ تم جلب ${documents?.length || 0} مستندات`);
      
      if (documents && documents.length > 0) {
        documents.forEach((doc, index) => {
          console.log(`   ${index + 1}. ${doc.title} - ID: ${doc.id}`);
          console.log(`      محتوى: ${doc.content ? 'موجود' : 'فارغ'}`);
        });
        
        // اختبار فتح مستند محدد
        console.log('\n🔧 6. اختبار فتح مستند محدد...');
        const testDoc = documents[0];
        
        const { data: fullDoc, error: fullDocError } = await supabase
          .from('documents')
          .select('*')
          .eq('id', testDoc.id)
          .single();
        
        if (fullDocError) {
          console.error('❌ خطأ في جلب المستند الكامل:', fullDocError.message);
        } else {
          console.log('✅ تم جلب المستند الكامل بنجاح');
          console.log(`   العنوان: ${fullDoc.title}`);
          console.log(`   المحتوى: ${fullDoc.content ? 'موجود' : 'فارغ'}`);
          console.log(`   الحالة: ${fullDoc.status || 'غير محدد'}`);
        }
      }
    }
    
    console.log('\n📊 7. ملخص التشخيص...');
    console.log('✅ المفاتيح موجودة في قاعدة البيانات');
    console.log('✅ يمكن تحويل المفاتيح إلى كائنات');
    console.log('✅ التوقيع والتحقق يعملان');
    console.log('✅ المستندات موجودة ويمكن جلبها');
    
    console.log('\n🤔 إذا كان كل شيء يعمل هنا، فالمشكلة قد تكون في:');
    console.log('   1. كود التطبيق نفسه (SvelteKit)');
    console.log('   2. مسار معين في التطبيق');
    console.log('   3. معالجة الأخطاء في الواجهة');
    console.log('   4. مشكلة في تحميل المكتبات في المتصفح');
    
    console.log('\n💡 الخطوات التالية:');
    console.log('   1. افتح المتصفح وانتقل إلى التطبيق');
    console.log('   2. افتح Developer Tools (F12)');
    console.log('   3. انتقل إلى Console tab');
    console.log('   4. جرب فتح مستند للتوقيع');
    console.log('   5. انظر إلى الأخطاء في Console');
    
    return true;
    
  } catch (error) {
    console.error('💥 خطأ عام في التشخيص:', error.message);
    console.error('📋 تفاصيل الخطأ:', error.stack);
    return false;
  }
}

diagnose500Error();
