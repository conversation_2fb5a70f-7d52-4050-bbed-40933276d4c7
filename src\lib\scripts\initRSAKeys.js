/**
 * سكريبت لتهيئة مفاتيح RSA في قاعدة البيانات
 */

import { RSAKeyService } from '../services/rsaKeyService.js';

async function initializeRSAKeys() {
  try {
    console.log('🔑 بدء تهيئة مفاتيح RSA...');
    
    // محاولة الحصول على المفاتيح الموجودة
    try {
      const existingKeys = await RSAKeyService.getActiveKeys();
      if (existingKeys && existingKeys.privateKeyObj && existingKeys.publicKeyObj) {
        console.log('✅ مفاتيح RSA موجودة بالفعل في قاعدة البيانات');
        return existingKeys;
      }
    } catch (error) {
      console.log('ℹ️ لا توجد مفاتيح RSA في قاعدة البيانات، سيتم إنشاء مفاتيح جديدة');
    }
    
    // إنشاء مفاتيح جديدة
    console.log('🔧 إنشاء مفاتيح RSA جديدة...');
    const newKeys = await RSAKeyService.generateAndStoreKeyPair();
    
    console.log('✅ تم إنشاء وحفظ مفاتيح RSA بنجاح في قاعدة البيانات');
    console.log('🔐 معرف المفتاح العام:', newKeys.publicKey.substring(0, 50) + '...');
    
    return newKeys;
  } catch (error) {
    console.error('❌ خطأ في تهيئة مفاتيح RSA:', error);
    throw error;
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeRSAKeys()
    .then(() => {
      console.log('🎉 تمت تهيئة مفاتيح RSA بنجاح');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في تهيئة مفاتيح RSA:', error);
      process.exit(1);
    });
}

export { initializeRSAKeys };
