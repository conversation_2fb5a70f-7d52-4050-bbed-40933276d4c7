# إصلاح سريع لخطأ 500 في نظام التوقيع الإلكتروني

## المشكلة الحالية
```
خطأ في جلب المستند: TypeError: fetch failed
```

## الحل السريع (خطوة بخطوة)

### 1. اختبار النظام أولاً
```bash
npm run test-system
```
هذا سيخبرك بالضبط ما هي المشكلة.

### 2. إذا كانت المشكلة في مفاتيح RSA
```bash
npm run init-rsa
```

### 3. إذا كانت المشكلة في الجداول
```bash
npm run check-rsa-table
```

### 4. تشخيص شامل
```bash
npm run diagnose-500
```

### 5. إعادة تشغيل التطبيق
```bash
npm run dev
```

## الإصلاحات المطبقة

### ✅ تم إصلاح:
1. **خطأ في خدمة RSA**: إصلاح استخدام المفتاح العام
2. **دوال غير متزامنة**: تحديث جميع دوال التوقيع
3. **معالجة الأخطاء**: تحسين رسائل الخطأ
4. **تحميل البيانات**: تبسيط تحميل البيانات من العميل

### 🛠️ أدوات جديدة:
- `npm run test-system` - اختبار سريع للنظام
- `npm run diagnose-500` - تشخيص شامل
- `npm run init-rsa` - إنشاء مفاتيح RSA
- `npm run fix-signature` - إصلاح شامل

## إذا استمر الخطأ

### تحقق من:
1. **اتصال الإنترنت**: تأكد من وجود اتصال مستقر
2. **إعدادات Supabase**: تحقق من URL و API keys
3. **المتصفح**: افتح Developer Tools وتحقق من Console
4. **الصلاحيات**: تأكد من تسجيل الدخول بحساب صحيح

### خطوات إضافية:
```bash
# إعادة تثبيت المكتبات
npm install

# مسح cache
npm run build

# إعادة تشغيل
npm run dev
```

## رسائل الخطأ الشائعة وحلولها

### "fetch failed"
- مشكلة في الاتصال بـ Supabase
- تحقق من إعدادات الشبكة
- تحقق من صحة URL في `src/lib/supabase.js`

### "relation does not exist"
- الجدول غير موجود في قاعدة البيانات
- قم بتشغيل: `npm run check-rsa-table`

### "permission denied"
- مشكلة في صلاحيات RLS
- تحقق من policies في Supabase Dashboard

### "Invalid API key"
- مفاتيح API غير صحيحة
- تحقق من ملف `.env` أو إعدادات Supabase

## للمطورين

### تشغيل الاختبارات:
```bash
# اختبار سريع
npm run test-system

# تشخيص مفصل
npm run diagnose-500

# اختبار مفاتيح RSA
npm run init-rsa
```

### فحص الأخطاء:
1. افتح Developer Tools (F12)
2. تحقق من تبويب Console
3. تحقق من تبويب Network
4. ابحث عن طلبات فاشلة (حمراء)

### تسجيل الأخطاء:
```javascript
// في المتصفح Console
console.log('Testing Supabase connection...');
```

## الدعم

إذا لم تنجح الحلول أعلاه:

1. **شغل التشخيص**: `npm run diagnose-500`
2. **انسخ رسالة الخطأ** من Console
3. **تحقق من سجلات Supabase** في Dashboard
4. **جرب في متصفح آخر** للتأكد

## ملاحظات مهمة

- ⚠️ تأكد من تسجيل الدخول قبل فتح المستندات
- ⚠️ تحقق من صلاحيات المستخدم للمستند
- ⚠️ تأكد من وجود اتصال إنترنت مستقر
- ⚠️ لا تشارك مفاتيح API الخاصة بك

---

**آخر تحديث**: تم تبسيط النظام وإزالة التعقيدات غير الضرورية
