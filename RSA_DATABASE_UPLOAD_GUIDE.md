# دليل رفع مفاتيح RSA إلى قاعدة البيانات

## 🎯 الهدف
نقل مفاتيح RSA من الملفات المحلية (temp-keys/) إلى قاعدة البيانات Supabase.

## 📋 المتطلبات المسبقة

### ✅ تأكد من وجود:
1. **التطبيق يعمل**: http://localhost:5175/
2. **ملفات المفاتيح موجودة**: 
   - `temp-keys/private-key.pem`
   - `temp-keys/public-key.pem`
3. **حساب مستخدم** مع صلاحيات مشرف

## 🚀 الطريقة الأولى: استخدام السكريبت (الأسهل)

### الخطوة 1: تسجيل الدخول في التطبيق
```bash
# تأكد من أن التطبيق يعمل
npm run dev
```

1. <PERSON><PERSON><PERSON><PERSON> المتصفح: http://localhost:5175/
2. سجل دخول أو أنشئ حساب جديد
3. تأكد من أن لديك صلاحيات مشرف

### الخطوة 2: تشغيل سكريبت الرفع
```bash
node upload-rsa-to-database.mjs
```

### الخطوة 3: التحقق من النتيجة
إذا نجح السكريبت، ستحصل على رسالة:
```
🎉 تم رفع مفاتيح RSA إلى قاعدة البيانات بنجاح!
```

## 🛠️ الطريقة الثانية: استخدام Supabase Dashboard (يدوياً)

### الخطوة 1: فتح Supabase Dashboard
1. اذهب إلى: https://supabase.com/dashboard
2. سجل دخول إلى مشروعك
3. انتقل إلى Table Editor
4. اختر جدول `rsa_keys`

### الخطوة 2: تحضير المفاتيح
```bash
# اعرض محتوى المفتاح الخاص
cat temp-keys/private-key.pem

# اعرض محتوى المفتاح العام  
cat temp-keys/public-key.pem
```

### الخطوة 3: إدراج المفاتيح يدوياً

#### إدراج المفتاح الخاص:
1. اضغط "Insert" → "Insert row"
2. املأ الحقول:
   - `key_type`: `private`
   - `key_pem`: انسخ محتوى `private-key.pem` كاملاً
   - `active`: `true`
3. اضغط "Save"

#### إدراج المفتاح العام:
1. اضغط "Insert" → "Insert row"
2. املأ الحقول:
   - `key_type`: `public`
   - `key_pem`: انسخ محتوى `public-key.pem` كاملاً
   - `active`: `true`
3. اضغط "Save"

## 🔧 الطريقة الثالثة: استخدام SQL مباشرة

### في Supabase SQL Editor:
```sql
-- تعطيل المفاتيح النشطة الحالية
UPDATE rsa_keys SET active = false WHERE active = true;

-- إدراج المفتاح الخاص (استبدل PRIVATE_KEY_CONTENT بالمحتوى الفعلي)
INSERT INTO rsa_keys (key_type, key_pem, active) 
VALUES ('private', 'PRIVATE_KEY_CONTENT', true);

-- إدراج المفتاح العام (استبدل PUBLIC_KEY_CONTENT بالمحتوى الفعلي)
INSERT INTO rsa_keys (key_type, key_pem, active) 
VALUES ('public', 'PUBLIC_KEY_CONTENT', true);

-- التحقق من المفاتيح المدرجة
SELECT id, key_type, active, created_at FROM rsa_keys WHERE active = true;
```

## 🔍 التحقق من نجاح العملية

### باستخدام السكريبت:
```bash
node check-database-status.mjs
```

### باستخدام SQL:
```sql
SELECT 
  id, 
  key_type, 
  active, 
  LENGTH(key_pem) as key_length,
  created_at 
FROM rsa_keys 
WHERE active = true;
```

يجب أن ترى:
- مفتاحين نشطين (private و public)
- طول المفتاح حوالي 1600+ حرف
- تاريخ إنشاء حديث

## ⚠️ استكشاف الأخطاء

### خطأ "row-level security policy"
**السبب**: لا تملك صلاحيات كافية
**الحل**:
1. تأكد من تسجيل الدخول
2. تحقق من دورك في جدول `profiles`
3. استخدم Supabase Dashboard بدلاً من السكريبت

### خطأ "المستخدم غير مسجل دخول"
**السبب**: انتهت جلسة المصادقة
**الحل**:
1. سجل دخول مرة أخرى في التطبيق
2. شغل السكريبت فوراً

### خطأ "ملفات المفاتيح غير موجودة"
**السبب**: لم يتم إنشاء المفاتيح المحلية
**الحل**:
```bash
node create-keys-simple.mjs
```

## 🎯 بعد رفع المفاتيح

### 1. اختبار النظام
```bash
# اختبار شامل
node test-updated-system.mjs

# أو اختبار قاعدة البيانات فقط
node check-database-status.mjs
```

### 2. إعادة تشغيل التطبيق
```bash
# أوقف التطبيق (Ctrl+C)
# ثم أعد تشغيله
npm run dev
```

### 3. اختبار التوقيع الإلكتروني
1. افتح مستند للتوقيع
2. تأكد من عدم ظهور أخطاء
3. جرب التوقيع والتحقق

## 🗑️ تنظيف الملفات المحلية (اختياري)

بعد نجاح رفع المفاتيح، يمكنك حذف الملفات المحلية:

```bash
# حذف مجلد المفاتيح المؤقتة
rm -rf temp-keys/

# أو في Windows
rmdir /s temp-keys
```

**⚠️ تحذير**: تأكد من نجاح رفع المفاتيح قبل الحذف!

## 📊 مقارنة الطرق

| الطريقة | السهولة | السرعة | الموثوقية |
|---------|---------|--------|-----------|
| السكريبت | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| Dashboard | ⭐⭐ | ⭐⭐ | ⭐⭐⭐ |
| SQL | ⭐ | ⭐⭐⭐ | ⭐⭐⭐ |

**التوصية**: ابدأ بالسكريبت، وإذا فشل استخدم Dashboard.

## 🎉 الخلاصة

بعد رفع المفاتيح بنجاح:
- ✅ النظام سيستخدم مفاتيح قاعدة البيانات
- ✅ أمان أعلى ومركزية أفضل
- ✅ سهولة في النسخ الاحتياطي والإدارة
- ✅ توافق مع بيئة الإنتاج

---

**💡 نصيحة**: احتفظ بنسخة احتياطية من المفاتيح في مكان آمن!
