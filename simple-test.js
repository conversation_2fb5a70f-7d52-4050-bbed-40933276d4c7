// اختبار بسيط بدون ES modules
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testBasicConnection() {
  console.log('🧪 اختبار الاتصال الأساسي...');
  
  try {
    // 1. اختبار الاتصال
    console.log('📊 1. اختبار الاتصال بقاعدة البيانات...');
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (testError) {
      console.error('❌ فشل الاتصال:', testError.message);
      return;
    }
    console.log('✅ الاتصال يعمل');

    // 2. اختبار جدول signed_documents
    console.log('📋 2. اختبار جدول signed_documents...');
    const { data: signedDocs, error: signedError } = await supabase
      .from('signed_documents')
      .select('id, status')
      .limit(3);
    
    if (signedError) {
      console.error('❌ خطأ في signed_documents:', signedError.message);
    } else {
      console.log(`✅ signed_documents يعمل (${signedDocs?.length || 0} مستندات)`);
    }

    // 3. اختبار جدول rsa_keys
    console.log('🔑 3. اختبار جدول rsa_keys...');
    const { data: rsaKeys, error: rsaError } = await supabase
      .from('rsa_keys')
      .select('id, key_type, active')
      .eq('active', true);
    
    if (rsaError) {
      console.error('❌ خطأ في rsa_keys:', rsaError.message);
      if (rsaError.message.includes('does not exist')) {
        console.log('💡 جدول rsa_keys غير موجود - يجب إنشاؤه');
      }
    } else {
      console.log(`✅ rsa_keys يعمل (${rsaKeys?.length || 0} مفاتيح نشطة)`);
    }

    // 4. اختبار المصادقة
    console.log('🔐 4. اختبار المصادقة...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('❌ خطأ في المصادقة:', authError.message);
    } else if (user) {
      console.log(`✅ مستخدم مسجل: ${user.email}`);
    } else {
      console.warn('⚠️ لا يوجد مستخدم مسجل');
    }

    console.log('\n🎯 خلاصة الاختبار:');
    console.log('- إذا كانت جميع الاختبارات ✅، فالمشكلة في الكود');
    console.log('- إذا كان هناك ❌، فالمشكلة في قاعدة البيانات');

  } catch (error) {
    console.error('💥 خطأ عام:', error.message);
  }
}

testBasicConnection();
