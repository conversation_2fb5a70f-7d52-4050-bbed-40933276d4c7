#!/usr/bin/env node

/**
 * سكريبت شامل لإصلاح نظام التوقيع الإلكتروني
 */

import { checkAndCreateRSATable } from './src/lib/scripts/checkRSATable.js';
import { initializeRSAKeys } from './src/lib/scripts/initRSAKeys.js';

async function fixSignatureSystem() {
  console.log('🔧 بدء إصلاح نظام التوقيع الإلكتروني...');
  console.log('=' .repeat(50));
  
  try {
    // الخطوة 1: التحقق من وجود جدول rsa_keys وإنشاؤه إذا لم يكن موجوداً
    console.log('\n📋 الخطوة 1: التحقق من جدول rsa_keys');
    await checkAndCreateRSATable();
    
    // الخطوة 2: تهيئة مفاتيح RSA
    console.log('\n🔑 الخطوة 2: تهيئة مفاتيح RSA');
    await initializeRSAKeys();
    
    console.log('\n' + '=' .repeat(50));
    console.log('✅ تم إصلاح نظام التوقيع الإلكتروني بنجاح!');
    console.log('\n📝 الخطوات التالية:');
    console.log('1. أعد تشغيل التطبيق');
    console.log('2. جرب فتح مستند للتوقيع');
    console.log('3. تأكد من عمل التوقيع والتحقق بشكل صحيح');
    
  } catch (error) {
    console.error('\n❌ فشل في إصلاح نظام التوقيع الإلكتروني:', error);
    console.log('\n🔍 تحقق من:');
    console.log('1. اتصال قاعدة البيانات');
    console.log('2. صلاحيات المستخدم في قاعدة البيانات');
    console.log('3. إعدادات Supabase');
    
    process.exit(1);
  }
}

// تشغيل السكريبت
fixSignatureSystem();
