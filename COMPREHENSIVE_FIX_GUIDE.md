# دليل الإصلاح الشامل لخطأ 500 في نظام التوقيع الإلكتروني

## خطوات التشخيص والإصلاح

### الخطوة 1: تشخيص المشكلة
```bash
# تشغيل سكريبت التشخيص الشامل
npm run diagnose-500
```

هذا السكريبت سيفحص:
- ✅ الاتصال بقاعدة البيانات
- ✅ وجود الجداول المطلوبة
- ✅ مفاتيح RSA
- ✅ صلاحيات المستخدم
- ✅ خدمة Storage

### الخطوة 2: إصلاح المشاكل المكتشفة

#### إذا كانت المشكلة في مفاتيح RSA:
```bash
# إنشاء مفاتيح RSA جديدة
npm run init-rsa

# أو تشغيل الإصلاح الشامل
npm run fix-signature
```

#### إذا كانت المشكلة في الجداول:
```bash
# فحص جدول rsa_keys
npm run check-rsa-table
```

### الخطوة 3: التحقق من الإصلاحات

#### 1. فحص المتصفح
- افتح Developer Tools (F12)
- تحقق من تبويب Console للأخطاء
- تحقق من تبويب Network للطلبات الفاشلة

#### 2. فحص سجلات الخادم
- تحقق من سجلات SvelteKit
- ابحث عن أخطاء JavaScript/TypeScript

#### 3. اختبار النظام
```bash
# إعادة تشغيل التطبيق
npm run dev

# ثم جرب:
# 1. فتح مستند للتوقيع
# 2. التوقيع على مستند
# 3. التحقق من التوقيع
```

## الإصلاحات المطبقة

### 1. إصلاح خدمة RSA Key Service ✅
- تم إصلاح خطأ في استخدام المفتاح العام
- تم تحديث الدوال لتكون غير متزامنة (async)

### 2. إضافة معالجة من جانب الخادم ✅
- تم إنشاء `+page.server.ts` للمستندات الموقعة
- تحميل البيانات من الخادم بدلاً من العميل فقط
- معالجة أفضل للأخطاء

### 3. تحسين معالجة الأخطاء ✅
- رسائل خطأ أكثر وضوحاً
- تسجيل مفصل للأخطاء
- معالجة آمنة للاستثناءات

### 4. أدوات التشخيص ✅
- سكريبت تشخيص شامل
- فحص جميع مكونات النظام
- اقتراحات إصلاح تلقائية

## الملفات الجديدة والمعدلة

### ملفات جديدة:
- `src/routes/dashboard/signed-documents/[id]/+page.server.ts` - معالجة الخادم
- `src/routes/dashboard/signed-documents/[id]/$types.ts` - تعريف الأنواع
- `src/lib/scripts/diagnose500Error.js` - تشخيص شامل
- `src/lib/scripts/initRSAKeys.js` - تهيئة مفاتيح RSA
- `src/lib/scripts/checkRSATable.js` - فحص جدول RSA
- `fix-signature-system.js` - إصلاح شامل
- `COMPREHENSIVE_FIX_GUIDE.md` - هذا الدليل

### ملفات معدلة:
- `src/lib/services/rsaKeyService.js` - إصلاح خطأ المفتاح العام
- `src/lib/utils/signatureUtils.js` - دوال غير متزامنة
- `src/routes/dashboard/signed-documents/[id]/+page.svelte` - استخدام بيانات الخادم
- `package.json` - سكريبتات جديدة

## استكشاف الأخطاء المتقدم

### إذا استمر خطأ 500:

#### 1. فحص إعدادات Supabase
```javascript
// في src/lib/supabase.js
console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseAnonKey?.substring(0, 20) + '...');
```

#### 2. فحص صلاحيات RLS
```sql
-- في Supabase SQL Editor
SELECT * FROM rsa_keys WHERE active = true;
SELECT * FROM signed_documents LIMIT 5;
```

#### 3. فحص مكتبات التشفير
```bash
# تأكد من تثبيت المكتبات
npm list node-forge crypto-js
```

#### 4. فحص متغيرات البيئة
```bash
# تحقق من ملف .env
cat .env | grep SUPABASE
```

### رسائل الخطأ الشائعة وحلولها:

#### "relation does not exist"
```bash
# الجدول غير موجود
npm run check-rsa-table
```

#### "permission denied"
```bash
# مشكلة في الصلاحيات
# تحقق من RLS policies في Supabase
```

#### "Invalid API key"
```bash
# مشكلة في مفاتيح API
# تحقق من إعدادات Supabase
```

#### "Cannot read property of undefined"
```bash
# مشكلة في البيانات
npm run diagnose-500
```

## الدعم والمساعدة

### إذا لم تنجح الحلول:

1. **شغل التشخيص الشامل**:
   ```bash
   npm run diagnose-500
   ```

2. **تحقق من سجلات المتصفح**:
   - افتح Developer Tools
   - تحقق من Console و Network tabs

3. **تحقق من سجلات الخادم**:
   - ابحث عن أخطاء في terminal
   - تحقق من Supabase logs

4. **إعادة تعيين النظام**:
   ```bash
   # إعادة تثبيت المكتبات
   npm install
   
   # إعادة إنشاء مفاتيح RSA
   npm run init-rsa
   
   # إعادة تشغيل التطبيق
   npm run dev
   ```

## ملاحظات مهمة

- ⚠️ تأكد من وجود اتصال إنترنت مستقر
- ⚠️ تحقق من صحة إعدادات Supabase
- ⚠️ تأكد من تسجيل الدخول بحساب له صلاحيات
- ⚠️ لا تشارك مفاتيح API الخاصة بك

## نصائح للمطورين

1. **استخدم دائماً `await` مع دوال التوقيع**
2. **تحقق من البيانات قبل استخدامها**
3. **استخدم try-catch لمعالجة الأخطاء**
4. **سجل الأخطاء بتفصيل للتشخيص**
5. **اختبر النظام بعد كل تغيير**
