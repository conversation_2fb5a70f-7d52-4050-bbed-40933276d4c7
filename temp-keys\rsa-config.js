// مفاتيح RSA المؤقتة
export const RSA_KEYS = {
  privateKey: `*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
  publicKey: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwxqSlfmiJ/ZUtlgWySvk
bMmpEdDs7tDAHTLd7Jo97Obd/wbWnTkrazt7/MnSCpYlZcczz6K7uS8D8pbJTRwq
XVYtRfAyrFIAQGxEJk88elj1xd5DuiucPHnG0FbDQBs3q4lXiMaeD8yr4nAO51tj
3QQFyN+LzO/W/Pwf8qYPV8ssK/QtqmUp47uioGaW/d9KqJhxVZnm1bhWJSOBnv8x
SfQbTVL6kS/MirUJdwna35cr/hjbyYN5uZH/DlQZC9KySFv+GbEYi7afP8iNloJG
tXwvClEZTcASog19uSmeSguLc/FND8Xpl3KMR8sFevddyoIOS1HtNE9z6Mj9/nvf
SQIDAQAB
-----END PUBLIC KEY-----
`
};

// استخدم هذه المفاتيح في حالة عدم توفر مفاتيح من قاعدة البيانات
export function getLocalRSAKeys() {
  const forge = require('node-forge');
  
  return {
    privateKey: RSA_KEYS.privateKey,
    publicKey: RSA_KEYS.publicKey,
    privateKeyObj: forge.pki.privateKeyFromPem(RSA_KEYS.privateKey),
    publicKeyObj: forge.pki.publicKeyFromPem(RSA_KEYS.publicKey)
  };
}
