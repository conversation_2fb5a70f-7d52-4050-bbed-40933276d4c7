import { createClient } from '@supabase/supabase-js';
import forge from 'node-forge';

console.log('🔑 إنشاء مفاتيح RSA...');
console.log('=' .repeat(40));

const supabaseUrl = 'https://ipbglrpzcziafodbyoqd.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlwYmdscnB6Y3ppYWZvZGJ5b3FkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDY2NTYsImV4cCI6MjA2MjkyMjY1Nn0.J6766KCmB3V7aqNPN1uXjcCfsR-IiLBrDb0LdIsEC80';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createRSAKeys() {
  try {
    console.log('🔧 إنشاء زوج مفاتيح RSA...');
    
    // إنشاء زوج مفاتيح RSA
    const keypair = forge.pki.rsa.generateKeyPair({ bits: 2048 });
    
    // تحويل المفاتيح إلى تنسيق PEM
    const privateKeyPem = forge.pki.privateKeyToPem(keypair.privateKey);
    const publicKeyPem = forge.pki.publicKeyToPem(keypair.publicKey);
    
    console.log('✅ تم إنشاء المفاتيح بنجاح');
    
    console.log('🔄 تعطيل المفاتيح النشطة الحالية...');
    // تعطيل المفاتيح النشطة الحالية
    await supabase
      .from('rsa_keys')
      .update({ active: false })
      .eq('active', true);
    
    console.log('💾 حفظ المفتاح الخاص...');
    // تخزين المفتاح الخاص
    const { error: privateKeyError } = await supabase
      .from('rsa_keys')
      .insert({
        key_type: 'private',
        key_pem: privateKeyPem,
        active: true
      });
    
    if (privateKeyError) {
      console.error('❌ خطأ في حفظ المفتاح الخاص:', privateKeyError.message);
      return false;
    }
    
    console.log('💾 حفظ المفتاح العام...');
    // تخزين المفتاح العام
    const { error: publicKeyError } = await supabase
      .from('rsa_keys')
      .insert({
        key_type: 'public',
        key_pem: publicKeyPem,
        active: true
      });
    
    if (publicKeyError) {
      console.error('❌ خطأ في حفظ المفتاح العام:', publicKeyError.message);
      return false;
    }
    
    console.log('✅ تم حفظ المفاتيح في قاعدة البيانات بنجاح');
    
    // التحقق من المفاتيح المحفوظة
    console.log('🔍 التحقق من المفاتيح المحفوظة...');
    const { data: savedKeys, error: checkError } = await supabase
      .from('rsa_keys')
      .select('id, key_type, active')
      .eq('active', true);
    
    if (checkError) {
      console.error('❌ خطأ في التحقق:', checkError.message);
    } else {
      console.log(`✅ تم العثور على ${savedKeys?.length || 0} مفاتيح نشطة`);
      savedKeys?.forEach(key => {
        console.log(`   - ${key.key_type}: ${key.id}`);
      });
    }
    
    console.log('\n🎉 تم إنشاء مفاتيح RSA بنجاح!');
    return true;
    
  } catch (error) {
    console.error('💥 خطأ في إنشاء مفاتيح RSA:', error.message);
    return false;
  }
}

createRSAKeys();
